#!/usr/bin/env python3
"""ETL执行脚本

提供命令行接口来执行全量和增量ETL处理。
"""

import argparse
import asyncio
import sys
from pathlib import Path
from typing import Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from dayu_fault_rag.config.settings import LoggingConfig, get_settings  # noqa: E402
from dayu_fault_rag.services.etl_service import (  # noqa: E402
    ETLService,
    create_etl_service,
)
from dayu_fault_rag.utils.logging import get_logger, setup_logging  # noqa: E402


async def run_full_etl(etl_service: ETLService) -> None:
    """运行全量ETL处理."""
    logger = get_logger(__name__)
    logger.info("=" * 50)
    logger.info("开始执行全量ETL处理")
    logger.info("=" * 50)

    try:
        # # 健康检查
        # if not await etl_service.health_check():
        #     logger.error("ETL服务健康检查失败，请检查配置和服务状态")
        #     return

        # 执行全量ETL
        stats = await etl_service.run_full_etl()

        # 打印统计信息
        logger.info("=" * 50)
        logger.info("全量ETL处理完成")
        logger.info("=" * 50)
        logger.info(f"抽取数据: {stats.extracted_count} 条")
        logger.info(f"转换成功: {stats.transformed_count} 条")
        logger.info(f"加载成功: {stats.loaded_count} 个文档")
        logger.info(f"跳过数据: {stats.skipped_count} 条")
        logger.info(f"错误数量: {stats.error_count} 条")
        logger.info(f"处理耗时: {stats.get_duration():.2f} 秒")

    except Exception as e:
        logger.error(f"全量ETL处理失败: {e}")
        raise


async def run_incremental_etl(etl_service: ETLService) -> None:
    """运行增量ETL处理."""
    logger = get_logger(__name__)
    logger.info("=" * 50)
    logger.info("开始执行增量ETL处理")
    logger.info("=" * 50)

    try:
        # 健康检查
        if not await etl_service.health_check():
            logger.error("ETL服务健康检查失败，请检查配置和服务状态")
            return

        # 执行增量ETL
        stats = await etl_service.run_incremental_etl()

        # 打印统计信息
        logger.info("=" * 50)
        logger.info("增量ETL处理完成")
        logger.info("=" * 50)
        logger.info(f"抽取数据: {stats.extracted_count} 条")
        logger.info(f"转换成功: {stats.transformed_count} 条")
        logger.info(f"加载成功: {stats.loaded_count} 个文档")
        logger.info(f"跳过数据: {stats.skipped_count} 条")
        logger.info(f"错误数量: {stats.error_count} 条")
        logger.info(f"处理耗时: {stats.get_duration():.2f} 秒")

    except Exception as e:
        logger.error(f"增量ETL处理失败: {e}")
        raise


async def show_etl_status(etl_service: ETLService) -> None:
    """显示ETL状态信息."""
    logger = get_logger(__name__)
    logger.info("=" * 50)
    logger.info("ETL状态信息")
    logger.info("=" * 50)

    try:
        status = await etl_service.get_etl_status()

        # 水印信息
        watermark = status.get("watermark", {})
        logger.info("水印信息:")
        logger.info(f"  最后更新时间: {watermark.get('last_update_time', 'N/A')}")
        logger.info(f"  最后处理故障ID: {watermark.get('last_processed_fid', 'N/A')}")
        logger.info(f"  总处理数量: {watermark.get('total_processed', 0)}")
        logger.info(f"  最后运行时间: {watermark.get('last_run_time', 'N/A')}")

        # MySQL统计
        mysql_stats = status.get("mysql_stats", {})
        logger.info("\nMySQL统计:")
        logger.info(f"  故障总数: {mysql_stats.get('total_faults', 0)}")
        logger.info(f"  最新更新时间: {mysql_stats.get('latest_update_time', 'N/A')}")

        # Milvus统计
        milvus_stats = status.get("milvus_stats", {})
        logger.info("\nMilvus统计:")
        logger.info(f"  集合名称: {milvus_stats.get('collection_name', 'N/A')}")
        logger.info(f"  连接状态: {milvus_stats.get('connected', False)}")
        logger.info(f"  向量维度: {milvus_stats.get('embedding_dimension', 0)}")

        # 嵌入服务统计
        embedding_stats = status.get("embedding_stats", {})
        logger.info("\n嵌入服务统计:")
        logger.info(f"  内存缓存大小: {embedding_stats.get('memory_cache_size', 0)}")
        logger.info(f"  磁盘缓存大小: {embedding_stats.get('disk_cache_size', 0)}")
        logger.info(
            f"  缓存启用状态: {embedding_stats.get('enable_disk_cache', False)}"
        )

        # 配置信息
        config = status.get("config", {})
        logger.info("\n配置信息:")
        logger.info(f"  批处理大小: {config.get('batch_size', 0)}")
        logger.info(f"  分块大小: {config.get('chunk_size', 0)}")
        logger.info(f"  同步间隔: {config.get('sync_interval', 0)} 秒")

    except Exception as e:
        logger.error(f"获取ETL状态失败: {e}")


async def test_etl_health(etl_service: ETLService) -> None:
    """测试ETL服务健康状态."""
    logger = get_logger(__name__)
    logger.info("=" * 50)
    logger.info("ETL服务健康检查")
    logger.info("=" * 50)

    try:
        is_healthy = await etl_service.health_check()

        if is_healthy:
            logger.info("✅ ETL服务健康检查通过")
        else:
            logger.error("❌ ETL服务健康检查失败")

    except Exception as e:
        logger.error(f"健康检查异常: {e}")


async def main():
    """主函数."""
    parser = argparse.ArgumentParser(description="大禹平台故障数据ETL处理工具")
    parser.add_argument(
        "command",
        choices=["full", "incremental", "status", "health"],
        help="ETL命令: full(全量处理), incremental(增量处理), status(状态查看), health(健康检查)",
    )
    parser.add_argument("--config", type=str, help="配置文件路径（可选）")
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别",
    )
    parser.add_argument("--batch-size", type=int, help="批处理大小（覆盖配置文件设置）")

    args = parser.parse_args()

    # 初始化日志配置
    settings = get_settings()

    # 创建自定义日志配置，使用命令行参数中的日志级别
    log_config = LoggingConfig(
        level=args.log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        file_path=settings.log_file,
    )

    # 设置日志配置
    setup_logging(log_config)

    # 获取日志记录器
    logger = get_logger(__name__)

    # 打印启动信息
    logger.info(f"大禹平台故障数据ETL工具 v{settings.version}")
    logger.info(f"运行环境: {settings.environment}")
    logger.info(f"执行命令: {args.command}")
    logger.info(f"日志级别: {args.log_level}")

    etl_service: Optional[ETLService] = None

    try:
        # 创建ETL服务实例
        logger.info("正在初始化ETL服务...")
        etl_service = await create_etl_service()
        logger.info("ETL服务初始化完成")

        # 如果指定了批处理大小，更新配置
        if args.batch_size:
            etl_service.config.batch_size = args.batch_size
            logger.info(f"批处理大小设置为: {args.batch_size}")

        # 执行相应的命令
        if args.command == "full":
            await run_full_etl(etl_service)
        elif args.command == "incremental":
            await run_incremental_etl(etl_service)
        elif args.command == "status":
            await show_etl_status(etl_service)
        elif args.command == "health":
            await test_etl_health(etl_service)

        logger.info("ETL工具执行完成")

    except KeyboardInterrupt:
        logger.info("ETL处理被用户中断")
    except Exception as e:
        logger.error(f"ETL工具执行失败: {e}", exc_info=True)
        sys.exit(1)
    finally:
        # 清理资源
        if etl_service:
            try:
                await etl_service.mysql_client.close()
                await etl_service.milvus_client.disconnect()
                logger.info("资源清理完成")
            except Exception as e:
                logger.warning(f"资源清理时发生错误: {e}")


if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())
