#!/bin/bash

# 大禹故障RAG系统 Docker部署脚本
# 支持开发环境和生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
大禹故障RAG系统 Docker部署脚本

用法: $0 [选项] [命令]

命令:
    dev         启动开发环境（包含MySQL和Milvus）
    prod        启动生产环境（仅应用服务）
    build       构建Docker镜像
    stop        停止所有服务
    clean       清理所有容器和数据卷
    logs        查看服务日志
    status      查看服务状态

选项:
    -h, --help  显示此帮助信息
    -v          详细输出

示例:
    $0 dev              # 启动开发环境
    $0 prod             # 启动生产环境
    $0 build            # 构建镜像
    $0 logs dayu-mcp    # 查看MCP服务日志
    $0 clean            # 清理环境

EOF
}

# 检查Docker和Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f ".env" ]; then
        log_warning ".env文件不存在，从env.example复制..."
        if [ -f "env.example" ]; then
            cp env.example .env
            log_info "请编辑.env文件配置必要的环境变量"
        else
            log_error "env.example文件不存在"
            exit 1
        fi
    fi
}

# 构建Docker镜像
build_image() {
    log_info "构建Docker镜像..."
    docker build -t dayu-fault-rag:latest .
    log_success "Docker镜像构建完成"
}

# 启动开发环境
start_dev() {
    log_info "启动开发环境（包含数据库服务）..."
    check_env_file
    
    # 创建必要的目录
    mkdir -p data logs
    
    # 启动开发环境服务
    docker-compose --profile dev up -d
    
    log_success "开发环境启动完成"
    log_info "服务访问地址："
    log_info "  - MCP服务: http://localhost:8001"
    log_info "  - MySQL: localhost:3306"
    log_info "  - Milvus: localhost:19530"
    log_info "  - MinIO控制台: http://localhost:9001"
}

# 启动生产环境
start_prod() {
    log_info "启动生产环境（仅应用服务）..."
    check_env_file
    
    # 创建必要的目录
    mkdir -p data logs
    
    # 启动生产环境服务
    docker-compose up -d dayu-etl dayu-mcp
    
    log_success "生产环境启动完成"
    log_info "服务访问地址："
    log_info "  - MCP服务: http://localhost:8001"
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose down
    log_success "服务已停止"
}

# 清理环境
clean_environment() {
    log_warning "这将删除所有容器、镜像和数据卷，确定要继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "清理Docker环境..."
        
        # 停止并删除容器
        docker-compose down -v --remove-orphans
        
        # 删除镜像
        docker rmi dayu-fault-rag:latest 2>/dev/null || true
        
        # 删除数据卷
        docker volume rm dayu_data dayu_logs dayu_mysql_data dayu_milvus_data dayu_etcd_data dayu_minio_data 2>/dev/null || true
        
        log_success "环境清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 查看日志
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        log_info "显示所有服务日志..."
        docker-compose logs -f
    else
        log_info "显示 $service 服务日志..."
        docker-compose logs -f "$service"
    fi
}

# 查看服务状态
show_status() {
    log_info "服务状态："
    docker-compose ps
    
    log_info "\n容器资源使用情况："
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

# 主函数
main() {
    # 检查系统要求
    check_requirements
    
    # 解析命令行参数
    case "${1:-}" in
        "dev")
            start_dev
            ;;
        "prod")
            start_prod
            ;;
        "build")
            build_image
            ;;
        "stop")
            stop_services
            ;;
        "clean")
            clean_environment
            ;;
        "logs")
            show_logs "$2"
            ;;
        "status")
            show_status
            ;;
        "-h"|"--help"|"help")
            show_help
            ;;
        "")
            log_error "请指定命令，使用 -h 查看帮助"
            exit 1
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
