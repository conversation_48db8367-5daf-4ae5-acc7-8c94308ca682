#!/usr/bin/env python3
"""
大禹故障RAG系统健康检查脚本
用于Docker容器健康检查和服务监控
"""

import asyncio
import sys
import time
from typing import Dict, List, Optional

import httpx
from pydantic import BaseModel


class HealthCheckResult(BaseModel):
    """健康检查结果模型."""
    
    service: str
    status: str  # "healthy", "unhealthy", "unknown"
    message: str
    response_time: Optional[float] = None
    details: Optional[Dict] = None


class HealthChecker:
    """健康检查器."""
    
    def __init__(self, timeout: int = 10):
        """初始化健康检查器.
        
        Args:
            timeout: 请求超时时间（秒）
        """
        self.timeout = timeout
        self.results: List[HealthCheckResult] = []
    
    async def check_database_connection(self) -> HealthCheckResult:
        """检查数据库连接."""
        try:
            from src.dayu_fault_rag.database.mysql_client import MySQLClient
            from src.dayu_fault_rag.config.settings import get_settings
            
            settings = get_settings()
            mysql_client = MySQLClient(settings.mysql)
            
            start_time = time.time()
            await mysql_client.connect()
            
            # 执行简单查询测试连接
            async with mysql_client.get_session() as session:
                result = await session.execute("SELECT 1")
                await result.fetchone()
            
            response_time = time.time() - start_time
            
            await mysql_client.disconnect()
            
            return HealthCheckResult(
                service="mysql",
                status="healthy",
                message="MySQL数据库连接正常",
                response_time=response_time
            )
            
        except Exception as e:
            return HealthCheckResult(
                service="mysql",
                status="unhealthy",
                message=f"MySQL数据库连接失败: {str(e)}"
            )
    
    async def check_milvus_connection(self) -> HealthCheckResult:
        """检查Milvus连接."""
        try:
            from src.dayu_fault_rag.database.milvus_client import MilvusClient
            from src.dayu_fault_rag.config.settings import get_settings
            
            settings = get_settings()
            milvus_client = MilvusClient(settings.milvus)
            
            start_time = time.time()
            
            # 检查连接和集合状态
            collection_info = await milvus_client.get_collection_info()
            response_time = time.time() - start_time
            
            return HealthCheckResult(
                service="milvus",
                status="healthy",
                message="Milvus向量数据库连接正常",
                response_time=response_time,
                details={"collection_info": collection_info}
            )
            
        except Exception as e:
            return HealthCheckResult(
                service="milvus",
                status="unhealthy",
                message=f"Milvus向量数据库连接失败: {str(e)}"
            )
    
    async def check_embedding_service(self) -> HealthCheckResult:
        """检查向量化服务."""
        try:
            from src.dayu_fault_rag.services.embedding_service import EmbeddingService
            from src.dayu_fault_rag.config.settings import get_settings
            
            settings = get_settings()
            embedding_service = EmbeddingService(settings.embedding)
            
            start_time = time.time()
            
            # 测试向量化功能
            test_text = "这是一个测试文本"
            embeddings = await embedding_service.embed_documents([test_text])
            
            response_time = time.time() - start_time
            
            if embeddings and len(embeddings) > 0:
                return HealthCheckResult(
                    service="embedding",
                    status="healthy",
                    message="向量化服务正常",
                    response_time=response_time,
                    details={"embedding_dimension": len(embeddings[0])}
                )
            else:
                return HealthCheckResult(
                    service="embedding",
                    status="unhealthy",
                    message="向量化服务返回空结果"
                )
                
        except Exception as e:
            return HealthCheckResult(
                service="embedding",
                status="unhealthy",
                message=f"向量化服务检查失败: {str(e)}"
            )
    
    async def check_application_health(self) -> HealthCheckResult:
        """检查应用程序整体健康状态."""
        try:
            # 检查基本导入
            from src.dayu_fault_rag.agent.workflow import create_fault_search_workflow
            from src.dayu_fault_rag.config.settings import get_settings
            
            start_time = time.time()
            
            # 检查配置加载
            settings = get_settings()
            
            # 检查工作流创建
            workflow = await create_fault_search_workflow()
            
            response_time = time.time() - start_time
            
            return HealthCheckResult(
                service="application",
                status="healthy",
                message="应用程序健康检查通过",
                response_time=response_time,
                details={
                    "environment": settings.environment,
                    "version": "0.1.0"
                }
            )
            
        except Exception as e:
            return HealthCheckResult(
                service="application",
                status="unhealthy",
                message=f"应用程序健康检查失败: {str(e)}"
            )
    
    async def run_all_checks(self) -> List[HealthCheckResult]:
        """运行所有健康检查."""
        checks = [
            self.check_application_health(),
            self.check_database_connection(),
            self.check_milvus_connection(),
            self.check_embedding_service(),
        ]
        
        self.results = await asyncio.gather(*checks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(self.results):
            if isinstance(result, Exception):
                processed_results.append(HealthCheckResult(
                    service=f"check_{i}",
                    status="unhealthy",
                    message=f"健康检查异常: {str(result)}"
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    def print_results(self, results: List[HealthCheckResult]) -> None:
        """打印健康检查结果."""
        print("=" * 60)
        print("大禹故障RAG系统健康检查报告")
        print("=" * 60)
        
        overall_status = "healthy"
        
        for result in results:
            status_icon = "✅" if result.status == "healthy" else "❌"
            print(f"{status_icon} {result.service.upper()}: {result.message}")
            
            if result.response_time:
                print(f"   响应时间: {result.response_time:.3f}s")
            
            if result.details:
                for key, value in result.details.items():
                    print(f"   {key}: {value}")
            
            if result.status != "healthy":
                overall_status = "unhealthy"
            
            print()
        
        print("=" * 60)
        status_icon = "✅" if overall_status == "healthy" else "❌"
        print(f"{status_icon} 整体状态: {overall_status.upper()}")
        print("=" * 60)
    
    def get_exit_code(self, results: List[HealthCheckResult]) -> int:
        """获取退出码."""
        for result in results:
            if result.status != "healthy":
                return 1
        return 0


async def main():
    """主函数."""
    checker = HealthChecker()
    
    try:
        results = await checker.run_all_checks()
        checker.print_results(results)
        exit_code = checker.get_exit_code(results)
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n健康检查被中断")
        sys.exit(1)
    except Exception as e:
        print(f"健康检查执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
