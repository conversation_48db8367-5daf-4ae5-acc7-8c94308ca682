#!/usr/bin/env python3
"""配置系统测试脚本.

验证 pydantic_settings 配置系统是否正常工作。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from dayu_fault_rag.config.settings import get_settings  # noqa: E402


def test_basic_config():
    """测试基础配置加载."""
    print("🔧 测试基础配置加载...")

    settings = get_settings()

    print(f"  项目名称: {settings.project_name}")
    print(f"  版本: {settings.version}")
    print(f"  环境: {settings.environment}")
    print(f"  调试模式: {settings.debug}")
    print("  ✅ 基础配置加载正常")


def test_database_config():
    """测试数据库配置."""
    print("\n🗄️ 测试数据库配置...")

    settings = get_settings()
    db_config = settings.database_config

    print(f"  MySQL主机: {db_config.host}")
    print(f"  MySQL端口: {db_config.port}")
    print(f"  MySQL用户名: {db_config.username}")
    print(f"  MySQL数据库: {db_config.database}")
    print(f"  连接池大小: {db_config.pool_size}")

    # 测试URL生成
    mysql_url = settings.get_mysql_url()
    print(f"  MySQL连接URL: {mysql_url}")
    print("  ✅ 数据库配置正常")


def test_milvus_config():
    """测试Milvus配置."""
    print("\n🔍 测试Milvus配置...")

    settings = get_settings()
    milvus_config = settings.milvus_config

    print(f"  Milvus主机: {milvus_config.host}")
    print(f"  Milvus端口: {milvus_config.port}")
    print(f"  Milvus集合: {milvus_config.collection_name}")
    print(f"  向量维度: {milvus_config.dimension}")
    print(f"  相似度计算: {milvus_config.metric_type}")
    print("  ✅ Milvus配置正常")


def test_embedding_config():
    """测试向量化配置."""
    print("\n🤖 测试向量化配置...")

    settings = get_settings()
    embedding_config = settings.embedding_config

    print(f"  服务URL: {embedding_config.base_url}")
    print(f"  模型名称: {embedding_config.model_name}")
    print(f"  超时时间: {embedding_config.timeout}秒")
    print(f"  批处理大小: {embedding_config.batch_size}")
    print(f"  向量维度: {embedding_config.dimension}")
    print("  ✅ 向量化配置正常")


def test_api_config():
    """测试API配置."""
    print("\n🌐 测试API配置...")

    settings = get_settings()
    api_config = settings.api_config

    print(f"  API主机: {api_config.host}")
    print(f"  API端口: {api_config.port}")
    print(f"  调试模式: {api_config.debug}")
    print(f"  CORS来源: {api_config.cors_origins}")
    print(f"  文档URL: {api_config.docs_url}")
    print("  ✅ API配置正常")


def test_environment_variables():
    """测试环境变量覆盖."""
    print("\n🔧 测试环境变量覆盖...")

    # 设置测试环境变量
    os.environ["DAYU_PROJECT_NAME"] = "Test Project"
    os.environ["DAYU_MYSQL_HOST"] = "test-mysql.example.com"
    os.environ["DAYU_API_PORT"] = "9000"

    # 重新获取设置（清除缓存）
    get_settings.cache_clear()
    settings = get_settings()

    print(f"  覆盖后项目名称: {settings.project_name}")
    print(f"  覆盖后MySQL主机: {settings.database_config.host}")
    print(f"  覆盖后API端口: {settings.api_config.port}")

    # 验证覆盖是否生效
    assert settings.project_name == "Test Project"
    assert settings.database_config.host == "test-mysql.example.com"
    assert settings.api_config.port == 9000

    print("  ✅ 环境变量覆盖正常")

    # 清理测试环境变量
    del os.environ["DAYU_PROJECT_NAME"]
    del os.environ["DAYU_MYSQL_HOST"]
    del os.environ["DAYU_API_PORT"]
    get_settings.cache_clear()


def test_type_validation():
    """测试类型验证."""
    print("\n✅ 测试类型验证...")

    # 测试无效的端口号
    os.environ["DAYU_MYSQL_PORT"] = "invalid_port"

    try:
        get_settings.cache_clear()
        settings = get_settings()
        # 这里应该抛出验证错误
        print("  ❌ 类型验证失败 - 应该抛出错误")
    except Exception as e:
        print(f"  ✅ 类型验证正常 - 捕获错误: {type(e).__name__}")

    # 清理
    del os.environ["DAYU_MYSQL_PORT"]
    get_settings.cache_clear()


def show_all_config():
    """显示完整配置信息."""
    print("\n📋 完整配置信息:")
    print("=" * 50)

    settings = get_settings()

    print("项目信息:")
    print(f"  名称: {settings.project_name}")
    print(f"  版本: {settings.version}")
    print(f"  环境: {settings.environment}")
    print(f"  调试: {settings.debug}")

    print("\nMySQL配置:")
    print(f"  主机: {settings.mysql_host}")
    print(f"  端口: {settings.mysql_port}")
    print(f"  用户: {settings.mysql_username}")
    print(f"  数据库: {settings.mysql_database}")

    print("\nMilvus配置:")
    print(f"  主机: {settings.milvus_host}")
    print(f"  端口: {settings.milvus_port}")
    print(f"  集合: {settings.milvus_collection}")

    print("\nAPI配置:")
    print(f"  主机: {settings.api_host}")
    print(f"  端口: {settings.api_port}")
    print(f"  调试: {settings.api_debug}")

    print("\n向量化配置:")
    print(f"  URL: {settings.embedding_base_url}")
    print(f"  模型: {settings.embedding_model}")
    print(f"  超时: {settings.embedding_timeout}")

    print("\n日志配置:")
    print(f"  级别: {settings.log_level}")
    print(f"  文件: {settings.log_file}")


def main():
    """主函数."""
    print("🧪 大禹故障RAG系统配置测试")
    print("=" * 50)

    try:
        test_basic_config()
        test_database_config()
        test_milvus_config()
        test_embedding_config()
        test_api_config()
        test_environment_variables()
        test_type_validation()
        show_all_config()

        print("\n🎉 所有配置测试通过！")
        print("\n💡 使用说明:")
        print("1. 复制 env.example 为 .env")
        print("2. 修改 .env 文件中的配置值")
        print("3. 或者直接设置环境变量（DAYU_*前缀）")
        print("4. 系统会自动加载并验证配置")

    except Exception as e:
        print(f"\n❌ 配置测试失败: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
