#!/usr/bin/env python3
"""ETL服务运行脚本

支持后台运行和定时同步的ETL服务。
"""

import asyncio
import signal
import sys
from pathlib import Path
from typing import Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from dayu_fault_rag.config.settings import get_settings  # noqa: E402
from dayu_fault_rag.services.etl_service import create_etl_service  # noqa: E402
from dayu_fault_rag.utils.logging import get_logger, setup_logging  # noqa: E402


class ETLServiceRunner:
    """ETL服务运行器."""

    def __init__(self):
        """初始化ETL服务运行器."""
        self.etl_service: Optional[object] = None
        self.running = False
        self.logger = get_logger(__name__)
        self.settings = get_settings()

    async def initialize(self):
        """初始化ETL服务."""
        try:
            self.logger.info("正在初始化ETL服务...")
            self.etl_service = await create_etl_service()
            self.logger.info("ETL服务初始化完成")
            return True
        except Exception as e:
            self.logger.error(f"ETL服务初始化失败: {e}")
            return False

    async def run_single_sync(self, sync_type: str = "incremental"):
        """运行单次同步."""
        if not self.etl_service:
            self.logger.error("ETL服务未初始化")
            return False

        try:
            self.logger.info(f"开始执行{sync_type}同步...")

            if sync_type == "full":
                stats = await self.etl_service.run_full_etl()
            else:
                stats = await self.etl_service.run_incremental_etl()

            self.logger.info(f"{sync_type}同步完成:")
            self.logger.info(f"  抽取数据: {stats.extracted_count} 条故障数据")
            self.logger.info(f"  转换成功: {stats.transformed_count} 条故障数据")
            self.logger.info(f"  加载成功: {stats.loaded_count} 个向量记录")
            self.logger.info(f"  处理耗时: {stats.get_duration():.2f} 秒")

            return True
        except Exception as e:
            self.logger.error(f"{sync_type}同步失败: {e}")
            return False

    async def run_continuous_sync(self):
        """运行持续同步服务."""
        if not self.etl_service:
            self.logger.error("ETL服务未初始化")
            return

        self.running = True
        sync_interval = self.settings.etl_config.sync_interval

        self.logger.info(f"启动ETL持续同步服务，同步间隔: {sync_interval}秒")

        while self.running:
            try:
                # 执行增量同步
                success = await self.run_single_sync("incremental")

                if success:
                    self.logger.info(
                        f"同步完成，等待 {sync_interval} 秒后进行下次同步..."
                    )
                else:
                    self.logger.warning("同步失败，将在下次间隔重试")

                # 等待下次同步
                await asyncio.sleep(sync_interval)

            except asyncio.CancelledError:
                self.logger.info("ETL服务被取消")
                break
            except Exception as e:
                self.logger.error(f"ETL服务运行异常: {e}")
                await asyncio.sleep(60)  # 异常时等待1分钟后重试

    def stop(self):
        """停止ETL服务."""
        self.logger.info("正在停止ETL服务...")
        self.running = False

    async def cleanup(self):
        """清理资源."""
        if self.etl_service:
            try:
                await self.etl_service.mysql_client.close()
                await self.etl_service.milvus_client.disconnect()
                self.logger.info("ETL服务资源清理完成")
            except Exception as e:
                self.logger.warning(f"资源清理时发生错误: {e}")


async def main():
    """主函数."""
    import argparse

    parser = argparse.ArgumentParser(description="ETL服务运行器")
    parser.add_argument(
        "--mode",
        choices=["single", "continuous"],
        default="continuous",
        help="运行模式: single(单次) 或 continuous(持续)",
    )
    parser.add_argument(
        "--sync-type",
        choices=["full", "incremental"],
        default="incremental",
        help="同步类型: full(全量) 或 incremental(增量)",
    )
    parser.add_argument("--log-level", default="INFO", help="日志级别")

    args = parser.parse_args()

    # 设置日志
    setup_logging(level=args.log_level)
    logger = get_logger(__name__)

    # 创建ETL服务运行器
    runner = ETLServiceRunner()

    # 设置信号处理
    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}，正在停止服务...")
        runner.stop()

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # 初始化ETL服务
        if not await runner.initialize():
            sys.exit(1)

        # 根据模式运行
        if args.mode == "single":
            success = await runner.run_single_sync(args.sync_type)
            sys.exit(0 if success else 1)
        else:
            await runner.run_continuous_sync()

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止服务...")
    except Exception as e:
        logger.error(f"ETL服务运行失败: {e}", exc_info=True)
        sys.exit(1)
    finally:
        await runner.cleanup()


if __name__ == "__main__":
    asyncio.run(main())
