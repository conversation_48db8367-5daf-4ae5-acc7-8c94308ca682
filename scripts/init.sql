-- 大禹故障RAG系统数据库初始化脚本
-- 用于Docker环境的MySQL数据库初始化

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS itmp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE itmp;

-- 创建故障信息表（示例结构）
CREATE TABLE IF NOT EXISTS fault_info (
    fid VARCHAR(50) PRIMARY KEY COMMENT '故障ID',
    title VARCHAR(500) COMMENT '故障标题',
    description TEXT COMMENT '故障描述',
    problem TEXT COMMENT '问题详情',
    solution TEXT COMMENT '解决方案',
    category VARCHAR(100) COMMENT '故障分类',
    severity VARCHAR(20) COMMENT '严重程度',
    status VARCHAR(20) COMMENT '状态',
    created_time DATETIME COMMENT '创建时间',
    updated_time DATETIME COMMENT '更新时间',
    lastUpdateTime DATETIME COMMENT '最后更新时间',
    playback JSON COMMENT '回放信息',
    improveAction JSON COMMENT '改进措施',
    INDEX idx_category (category),
    INDEX idx_severity (severity),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time),
    INDEX idx_last_update_time (lastUpdateTime)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='故障信息表';

-- 插入示例数据
INSERT IGNORE INTO fault_info (
    fid, title, description, problem, solution, category, severity, status, 
    created_time, updated_time, lastUpdateTime
) VALUES 
(
    'FAULT_001',
    '用户登录失败',
    '用户反馈无法正常登录系统',
    '登录接口返回500错误，数据库连接超时',
    '重启数据库服务，优化连接池配置',
    '系统故障',
    'HIGH',
    'RESOLVED',
    '2024-01-01 10:00:00',
    '2024-01-01 12:00:00',
    '2024-01-01 12:00:00'
),
(
    'FAULT_002', 
    '支付接口异常',
    '用户支付时出现异常，订单状态不一致',
    '支付回调处理失败，事务回滚异常',
    '修复支付回调逻辑，增加重试机制',
    '业务故障',
    'CRITICAL',
    'RESOLVED',
    '2024-01-02 14:30:00',
    '2024-01-02 16:45:00',
    '2024-01-02 16:45:00'
),
(
    'FAULT_003',
    '数据同步延迟',
    '数据同步任务执行缓慢，影响业务查询',
    'ETL任务处理大量数据时内存不足',
    '优化ETL任务，增加分批处理逻辑',
    '性能问题',
    'MEDIUM',
    'RESOLVED',
    '2024-01-03 09:15:00',
    '2024-01-03 11:30:00',
    '2024-01-03 11:30:00'
);

-- 创建用户权限（如果需要）
-- GRANT ALL PRIVILEGES ON itmp.* TO 'dayu'@'%';
-- FLUSH PRIVILEGES;

-- 显示创建的表
SHOW TABLES;

-- 显示故障信息表结构
DESCRIBE fault_info;
