#!/usr/bin/env python3
"""数据库初始化脚本.

初始化MySQL和Milvus数据库连接，创建必要的表和集合。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from dayu_fault_rag.config.settings import get_settings
from dayu_fault_rag.utils.logging import get_logger, setup_logging


async def setup_mysql() -> bool | None:
    """设置MySQL数据库连接测试."""
    logger = get_logger(__name__)
    settings = get_settings()

    logger.info("开始测试MySQL数据库连接...")

    try:
        import aiomysql

        # 测试数据库连接
        connection = await aiomysql.connect(
            host=settings.mysql_host,
            port=settings.mysql_port,
            user=settings.mysql_username,
            password=settings.mysql_password,
            db=settings.mysql_database,
            charset="utf8mb4",
        )

        async with connection.cursor() as cursor:
            # 测试查询
            await cursor.execute("SELECT VERSION()")
            version = await cursor.fetchone()
            logger.info(f"MySQL版本: {version[0]}")

            # 检查fault_info表是否存在
            await cursor.execute(
                """
                SELECT COUNT(*)
                FROM information_schema.tables
                WHERE table_schema = %s AND table_name = 'fault_info'
            """,
                (settings.mysql_database,),
            )

            table_exists = await cursor.fetchone()
            if table_exists[0] > 0:
                logger.info("fault_info表已存在")

                # 查询表结构
                await cursor.execute("DESCRIBE fault_info")
                columns = await cursor.fetchall()
                logger.info(f"fault_info表包含 {len(columns)} 个字段")

                # 查询数据量
                await cursor.execute("SELECT COUNT(*) FROM fault_info")
                count = await cursor.fetchone()
                logger.info(f"fault_info表包含 {count[0]} 条记录")
            else:
                logger.warning("fault_info表不存在，请确认数据库配置")

        connection.close()
        logger.info("MySQL数据库连接测试成功")
        return True

    except Exception as e:
        logger.error(f"MySQL数据库连接失败: {e}")
        return False


async def setup_milvus() -> bool | None:
    """设置Milvus向量数据库."""
    logger = get_logger(__name__)
    settings = get_settings()

    logger.info("开始设置Milvus向量数据库...")

    try:
        from pymilvus import (
            Collection,
            CollectionSchema,
            DataType,
            FieldSchema,
            connections,
            utility,
        )

        # 连接Milvus
        connections.connect(
            alias="default",
            host=settings.milvus_host,
            port=settings.milvus_port,
            user=settings.milvus_username or "",
            password=settings.milvus_password or "",
        )

        logger.info("Milvus连接成功")

        # 检查集合是否存在
        collection_name = settings.milvus_collection
        if utility.has_collection(collection_name):
            logger.info(f"集合 {collection_name} 已存在")

            # 获取集合信息
            collection = Collection(collection_name)
            logger.info(f"集合包含 {collection.num_entities} 个向量")
        else:
            logger.info(f"创建新集合 {collection_name}")

            # 定义集合schema
            fields = [
                FieldSchema(
                    name="id", dtype=DataType.INT64, is_primary=True, auto_id=True
                ),
                FieldSchema(name="fid", dtype=DataType.VARCHAR, max_length=64),
                FieldSchema(name="content", dtype=DataType.VARCHAR, max_length=65535),
                FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=768),
                FieldSchema(name="metadata", dtype=DataType.JSON),
            ]

            schema = CollectionSchema(fields=fields, description="故障向量集合")

            # 创建集合
            collection = Collection(name=collection_name, schema=schema)

            # 创建索引
            index_params = {
                "metric_type": "COSINE",
                "index_type": "IVF_FLAT",
                "params": {"nlist": 128},
            }

            collection.create_index(field_name="vector", index_params=index_params)

            logger.info(f"集合 {collection_name} 创建成功")

        # 断开连接
        connections.disconnect("default")
        logger.info("Milvus设置完成")
        return True

    except Exception as e:
        logger.error(f"Milvus设置失败: {e}")
        return False


async def main() -> int:
    """主函数."""
    # 设置日志
    setup_logging()
    logger = get_logger(__name__)

    logger.info("开始数据库初始化...")

    # 测试MySQL连接
    mysql_ok = await setup_mysql()

    # 设置Milvus
    milvus_ok = await setup_milvus()

    if mysql_ok and milvus_ok:
        logger.info("数据库初始化完成")
        return 0
    else:
        logger.error("数据库初始化失败")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
