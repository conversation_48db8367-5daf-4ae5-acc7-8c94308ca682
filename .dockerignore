# 大禹故障RAG系统 Docker构建忽略文件
# 排除不必要的文件以优化构建速度和镜像大小

# ===========================================
# Git相关
# ===========================================
.git
.gitignore
.gitattributes
.github/

# ===========================================
# Python相关
# ===========================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ===========================================
# 虚拟环境
# ===========================================
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# ===========================================
# IDE和编辑器
# ===========================================
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# ===========================================
# 测试和覆盖率
# ===========================================
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# ===========================================
# 文档
# ===========================================
docs/_build/
docs/build/
*.md
!README.md

# ===========================================
# 日志文件
# ===========================================
*.log
logs/
log/

# ===========================================
# 临时文件
# ===========================================
*.tmp
*.temp
.cache/
.mypy_cache/
.dmypy.json
dmypy.json

# ===========================================
# 数据文件
# ===========================================
data/
*.db
*.sqlite
*.sqlite3

# ===========================================
# 配置文件
# ===========================================
.env.local
.env.development
.env.test
.env.production
config/local.yaml
config/development.yaml

# ===========================================
# Docker相关
# ===========================================
Dockerfile*
docker-compose*.yml
.dockerignore

# ===========================================
# CI/CD
# ===========================================
.github/
.gitlab-ci.yml
Jenkinsfile
.travis.yml
.circleci/

# ===========================================
# 其他
# ===========================================
README.md
LICENSE
CHANGELOG.md
CONTRIBUTING.md
.editorconfig
.pre-commit-config.yaml
pyproject.toml.bak
requirements*.txt
