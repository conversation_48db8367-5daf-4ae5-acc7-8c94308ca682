"""系统配置管理.

统一的配置管理类，支持从环境变量和配置文件加载设置。
"""

import os
from functools import lru_cache
from pathlib import Path
from typing import Any

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseConfig(BaseModel):
    """数据库配置."""

    host: str = Field(default="localhost", description="数据库主机地址")
    port: int = Field(default=3306, description="数据库端口")
    username: str = Field(default="root", description="数据库用户名")
    password: str = Field(default="", description="数据库密码")
    database: str = Field(default="itmp", description="数据库名称")
    charset: str = Field(default="utf8mb4", description="字符集")
    pool_size: int = Field(default=10, description="连接池大小")
    max_overflow: int = Field(default=20, description="连接池最大溢出")

    # 数据过滤配置
    table_name: str = Field(default="fault_info", description="故障信息表名")
    report_type_filter: str = Field(default="故障", description="报告类型过滤条件")
    stage_filter: str = Field(default="已发布", description="阶段过滤条件")


class LLMConfig(BaseModel):
    """LLM配置."""

    provider: str = Field(default="openai", description="模型提供商")
    model: str = Field(default="Qwen/Qwen3-32B", description="模型名称")
    max_tokens: int = Field(default=4096, description="最大令牌数")
    external_args: dict[str, Any] = Field(default={}, description="其他参数参数")
    extra_body: dict[str, Any] = Field(default={}, description="额外请求体")


class MilvusConfig(BaseModel):
    """Milvus配置."""

    uri: str = Field(default="localhost", description="Milvus主机地址")
    username: str = Field(default="", description="Milvus用户名")
    password: str = Field(default="", description="Milvus密码")
    collection_name: str = Field(default="fault_vectors", description="集合名称")
    dimension: int = Field(default=2560, description="向量维度，与嵌入模型保持一致")
    metric_type: str = Field(default="COSINE", description="相似度计算方式")
    database: str = Field(default="yw_ops_db", description="数据库名称")
    index_params: dict[str, Any] = Field(default={}, description="索引参数")
    search_params: dict[str, Any] = Field(default={}, description="搜索参数")


class EmbeddingConfig(BaseModel):
    """向量化模型配置."""

    base_url: str = Field(
        default="http://ray.ttyuyin.com:10001/ray-qwen3-emb",
        description="嵌入服务基础URL",
    )
    model_name: str = Field(
        default="Qwen/Qwen3-Embedding-4B", description="嵌入模型名称"
    )
    timeout: int = Field(default=30, description="请求超时时间")
    max_length: int = Field(default=512, description="最大文本长度")
    batch_size: int = Field(default=2, description="批处理大小")
    dimension: int = Field(
        default=2560, description="向量维度，Qwen3-Embedding-4B为2560维"
    )


class APIConfig(BaseModel):
    """API配置."""

    host: str = Field(default="0.0.0.0", description="API服务主机")
    port: int = Field(default=8000, description="API服务端口")
    debug: bool = Field(default=False, description="调试模式")
    cors_origins: list[str] = Field(default=["*"], description="CORS允许的来源")
    docs_url: str = Field(default="/docs", description="API文档URL")
    openapi_url: str = Field(default="/openapi.json", description="OpenAPI文档URL")


class LoggingConfig(BaseModel):
    """日志配置."""

    level: str = Field(default="DEBUG", description="日志级别")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式",
    )
    file_path: str | None = Field(default=None, description="日志文件路径")
    max_bytes: int = Field(default=10 * 1024 * 1024, description="日志文件最大大小")
    backup_count: int = Field(default=5, description="日志文件备份数量")


class ETLConfig(BaseModel):
    """ETL配置."""

    batch_size: int = Field(default=100, description="批处理大小")
    sync_interval: int = Field(default=3600, description="同步间隔（秒）")
    chunk_size: int = Field(default=2000, description="文本分块大小")
    chunk_overlap: int = Field(default=500, description="文本分块重叠")
    watermark_file: str = Field(
        default="etl_watermark.json", description="水印文件路径"
    )

    # 数据过滤配置
    report_type_filter: str = Field(default="故障", description="报告类型过滤条件")
    stage_filter: str = Field(default="已发布", description="阶段过滤条件")

    # 可选的额外过滤条件
    enabled_filters: dict[str, str] = Field(
        default_factory=lambda: {"reportType": "故障", "stage": "已发布"},
        description="启用的过滤条件字典",
    )


class SearchConfig(BaseModel):
    """搜索配置."""

    top_k: int = Field(default=50, description="相似性搜索返回结果数量")
    similarity_threshold: float = Field(default=0.5, description="相似性阈值")
    rerank_top_n: int = Field(default=5, description="重排序返回结果数量")
    rerank_threshold: float = Field(default=0.3, description="重排序阈值")


class Settings(BaseSettings):
    """系统设置类.

    从环境变量和配置文件加载系统配置。
    """

    # 环境配置
    environment: str = Field(default="development", description="运行环境")
    project_name: str = Field(default="Dayu Fault RAG System", description="项目名称")
    version: str = Field(default="0.1.0", description="项目版本")
    debug: bool = Field(default=False, description="调试模式")

    # ETL配置
    etl_batch_size: int = Field(default=200, description="ETL批处理大小")
    etl_sync_interval: int = Field(default=3600, description="ETL同步间隔（秒）")
    etl_chunk_size: int = Field(default=1000, description="ETL文本分块大小")
    etl_chunk_overlap: int = Field(default=200, description="ETL文本分块重叠")
    etl_watermark_file: str = Field(
        default="etl_watermark.json", description="ETL水印文件路径"
    )
    etl_report_type_filter: str = Field(default="故障", description="报告类型过滤条件")
    etl_stage_filter: str = Field(default="已发布", description="阶段过滤条件")

    # 数据库配置
    mysql_host: str = Field(default="localhost", description="MySQL主机")
    mysql_port: int = Field(default=3306, description="MySQL端口")
    mysql_username: str = Field(default="root", description="MySQL用户名")
    mysql_password: str = Field(default="", description="MySQL密码")
    mysql_database: str = Field(default="itmp", description="MySQL数据库名")

    # Milvus配置
    milvus_uri: str = Field(default="localhost:19530", description="Milvus主机")
    milvus_username: str = Field(default="", description="Milvus用户名")
    milvus_password: str = Field(default="", description="Milvus密码")
    milvus_collection: str = Field(
        default="dayu_fault_vectors", description="Milvus集合名"
    )
    milvus_database: str = Field(default="yw_ops_db", description="Milvus数据库名")
    milvus_index_params: dict[str, Any] = Field(
        default={"metric_type": "COSINE", "index_type": "FLAT"},
        description="Milvus索引参数",
    )
    milvus_search_params: dict[str, Any] = Field(
        default={"metric_type": "COSINE", "search_params": {"nprobe": 10}},
        description="Milvus搜索参数",
    )

    # API配置
    api_host: str = Field(default="0.0.0.0", description="API主机")
    api_port: int = Field(default=8000, description="API端口")
    api_debug: bool = Field(default=False, description="API调试模式")

    # 向量化配置
    embedding_base_url: str = Field(
        default="http://ray.ttyuyin.com:10001/ray-qwen3-emb", description="嵌入服务URL"
    )
    embedding_model: str = Field(
        default="Qwen/Qwen3-Embedding-4B", description="嵌入模型"
    )
    embedding_timeout: int = Field(default=30, description="嵌入服务超时时间")

    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_file: str | None = Field(default=None, description="日志文件")

    # 搜索配置
    search_top_k: int = Field(default=50, description="相似性搜索返回结果数量")
    search_similarity_threshold: float = Field(default=0.5, description="相似性阈值")
    search_rerank_top_n: int = Field(default=5, description="重排序返回结果数量")
    search_rerank_threshold: float = Field(default=0.3, description="重排序阈值")

    # LLM配置
    llm_provider: str = Field(default="openai", description="模型提供商")
    llm_model: str = Field(default="Qwen/Qwen3-32B", description="模型名称")
    llm_max_tokens: int = Field(default=4096, description="最大令牌数")
    llm_external_args: dict[str, Any] = Field(default={}, description="外部参数")
    llm_extra_body: dict[str, Any] = Field(default={}, description="额外请求体")

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_prefix="DAYU_",
        case_sensitive=False,
        extra="ignore",
    )

    @property
    def database_config(self) -> DatabaseConfig:
        """获取数据库配置."""
        return DatabaseConfig(
            host=self.mysql_host,
            port=self.mysql_port,
            username=self.mysql_username,
            password=self.mysql_password,
            database=self.mysql_database,
        )

    @property
    def milvus_config(self) -> MilvusConfig:
        """获取Milvus配置."""
        return MilvusConfig(
            uri=self.milvus_uri,
            username=self.milvus_username,
            password=self.milvus_password,
            collection_name=self.milvus_collection,
            database=self.milvus_database,
            index_params=self.milvus_index_params,
            search_params=self.milvus_search_params,
        )

    @property
    def embedding_config(self) -> EmbeddingConfig:
        """获取向量化配置."""
        return EmbeddingConfig(
            base_url=self.embedding_base_url,
            model_name=self.embedding_model,
            timeout=self.embedding_timeout,
        )

    @property
    def api_config(self) -> APIConfig:
        """获取API配置."""
        return APIConfig(
            host=self.api_host,
            port=self.api_port,
            debug=self.api_debug,
        )

    @property
    def logging_config(self) -> LoggingConfig:
        """获取日志配置."""
        return LoggingConfig(
            level=self.log_level,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            file_path=self.log_file,
        )

    @property
    def etl_config(self) -> ETLConfig:
        """获取ETL配置."""
        return ETLConfig(
            batch_size=self.etl_batch_size,
            sync_interval=self.etl_sync_interval,
            chunk_size=self.etl_chunk_size,
            chunk_overlap=self.etl_chunk_overlap,
            watermark_file=self.etl_watermark_file,
        )

    def get_mysql_url(self) -> str:
        """获取MySQL连接URL."""
        config = self.database_config
        return f"mysql+pymysql://{config.username}:{config.password}@{config.host}:{config.port}/{config.database}?charset={config.charset}"

    def get_async_mysql_url(self) -> str:
        """获取异步MySQL连接URL."""
        config = self.database_config
        return f"mysql+aiomysql://{config.username}:{config.password}@{config.host}:{config.port}/{config.database}?charset={config.charset}"

    @property
    def search_config(self) -> SearchConfig:
        """获取搜索配置."""
        return SearchConfig(
            top_k=self.search_top_k,
            similarity_threshold=self.search_similarity_threshold,
            rerank_top_n=self.search_rerank_top_n,
            rerank_threshold=self.search_rerank_threshold,
        )

    @property
    def llm_config(self) -> LLMConfig:
        """获取LLM配置."""
        return LLMConfig(
            provider=self.llm_provider,
            model=self.llm_model,
            max_tokens=self.llm_max_tokens,
            external_args=self.llm_external_args,
            extra_body=self.llm_extra_body,
        )

    def load_config_file(self, config_path: str) -> dict[str, Any]:
        """从配置文件加载配置."""
        import yaml

        if not os.path.exists(config_path):
            return {}

        with open(config_path, encoding="utf-8") as f:
            return yaml.safe_load(f) or {}


@lru_cache
def get_settings() -> Settings:
    """获取系统设置（单例模式）."""
    return Settings()


def get_project_root() -> Path:
    """获取项目根目录."""
    return Path(__file__).parent.parent.parent.parent


def get_config_dir() -> Path:
    """获取配置目录."""
    return get_project_root() / "config"


def get_logs_dir() -> Path:
    """获取日志目录."""
    logs_dir = get_project_root() / "logs"
    logs_dir.mkdir(exist_ok=True)
    return logs_dir
