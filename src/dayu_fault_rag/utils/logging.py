"""日志配置系统.

统一的日志配置和管理。
"""

import logging
import logging.handlers
import sys
from pathlib import Path

from ..config.settings import LoggingConfig, get_logs_dir


def setup_logging(config: LoggingConfig | None = None) -> None:
    """设置系统日志配置.

    Args:
        config: 日志配置对象，如果为None则使用默认配置
    """
    if config is None:
        from ..config.settings import get_settings

        config = get_settings().logging_config

    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.level.upper()))

    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建格式化器
    formatter = logging.Formatter(config.format)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, config.level.upper()))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 文件处理器（如果配置了文件路径）
    if config.file_path:
        file_path = Path(config.file_path)

        # 如果是相对路径，相对于日志目录
        if not file_path.is_absolute():
            file_path = get_logs_dir() / file_path

        # 确保日志目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)

        # 创建旋转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            filename=str(file_path),
            maxBytes=config.max_bytes,
            backupCount=config.backup_count,
            encoding="utf-8",
        )
        file_handler.setLevel(getattr(logging, config.level.upper()))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # 设置第三方库的日志级别
    _configure_third_party_loggers()


def _configure_third_party_loggers() -> None:
    """配置第三方库的日志级别."""
    # 降低第三方库的日志级别
    third_party_loggers = [
        "urllib3.connectionpool",
        "pymysql",
        "sqlalchemy.engine",
        "sqlalchemy.pool",
        "httpx",
        "httpcore",
        "uvicorn.access",
        "uvicorn.error",
        "fastapi",
        "pymilvus",
    ]

    for logger_name in third_party_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """获取日志记录器.

    Args:
        name: 日志记录器名称，通常使用 __name__

    Returns:
        配置好的日志记录器
    """
    return logging.getLogger(name)


class StructuredLogger:
    """结构化日志记录器.

    提供结构化的日志记录功能，支持上下文信息。
    """

    def __init__(self, name: str) -> None:
        self.logger = get_logger(name)
        self.context = {}

    def set_context(self, **kwargs) -> None:
        """设置日志上下文."""
        self.context.update(kwargs)

    def clear_context(self) -> None:
        """清除日志上下文."""
        self.context.clear()

    def _format_message(self, message: str, **kwargs) -> str:
        """格式化日志消息."""
        context = {**self.context, **kwargs}
        if context:
            context_str = " | ".join(f"{k}={v}" for k, v in context.items())
            return f"{message} | {context_str}"
        return message

    def debug(self, message: str, **kwargs) -> None:
        """记录DEBUG级别日志."""
        self.logger.debug(self._format_message(message, **kwargs))

    def info(self, message: str, **kwargs) -> None:
        """记录INFO级别日志."""
        self.logger.info(self._format_message(message, **kwargs))

    def warning(self, message: str, **kwargs) -> None:
        """记录WARNING级别日志."""
        self.logger.warning(self._format_message(message, **kwargs))

    def error(self, message: str, **kwargs) -> None:
        """记录ERROR级别日志."""
        self.logger.error(self._format_message(message, **kwargs))

    def critical(self, message: str, **kwargs) -> None:
        """记录CRITICAL级别日志."""
        self.logger.critical(self._format_message(message, **kwargs))

    def exception(self, message: str, **kwargs) -> None:
        """记录异常日志."""
        self.logger.exception(self._format_message(message, **kwargs))
