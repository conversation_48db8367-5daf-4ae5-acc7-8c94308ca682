"""大禹平台相似故障检索系统 (MVP版本).

基于RAG技术的智能故障分析应用，为运维人员提供相似故障检索和智能问答功能。
"""

__version__ = "0.1.0"
__author__ = "Dayu Team"
__description__ = "RAG-based fault similarity detection system using LangGraph"

# 导出核心组件
from .config.settings import Settings
from .models.fault import FaultInfo
from .models.vector_document import FaultDocument

__all__ = [
    "Settings",
    "FaultInfo",
    "FaultDocument",
    "__version__",
    "__author__",
    "__description__",
]
