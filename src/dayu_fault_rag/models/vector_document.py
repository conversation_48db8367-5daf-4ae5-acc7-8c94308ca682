"""向量文档模型

基于<PERSON><PERSON>hain生态的简化向量文档模型，专为langchain-milvus集成优化。
"""

from typing import Any, Dict, Optional

from langchain_core.documents import Document
from pydantic import BaseModel, Field


class FaultDocument(Document):
    """故障文档模型 - 简化的langchain Document扩展.

    专为Milvus向量存储优化，使用metadata存储所有业务字段。
    """

    chunk_index: int = 0
    total_chunks: int = 0

    def __init__(
        self,
        page_content: str | None = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs,
    ):
        """初始化故障文档.

        Args:
            page_content: 文档内容文本
            fid: 故障ID，作为主标识
            metadata: 元数据字典，包含所有业务字段
            **kwargs: 其他元数据字段
        """
        # 合并所有元数据
        combined_metadata = {**(metadata or {}), **kwargs}

        super().__init__(page_content=page_content, metadata=combined_metadata)

    @property
    def fid(self) -> str:
        """获取故障ID."""
        return self.metadata.get("fid", "")


class VectorSearchResult(BaseModel):
    """向量搜索结果."""

    document: FaultDocument = Field(..., description="匹配的文档")
    score: float = Field(..., description="相似度分数")

    class Config:
        arbitrary_types_allowed = True


class SearchRequest(BaseModel):
    """搜索请求模型."""

    query: str = Field(..., description="搜索查询文本")
    top_k: int = Field(default=10, description="返回结果数量")
    filter_dict: Optional[Dict[str, Any]] = Field(None, description="过滤条件")
    score_threshold: float = Field(default=0.0, description="相似度阈值")
