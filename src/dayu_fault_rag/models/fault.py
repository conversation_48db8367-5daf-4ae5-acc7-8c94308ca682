"""故障信息模型

定义故障信息的数据结构，映射MySQL中fault_info表的字段。
"""

import json
from datetime import datetime
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field, field_validator

from src.dayu_fault_rag.utils.logging import get_logger

logger = get_logger(__name__)


class FaultInfo(BaseModel):
    """故障信息模型.

    映射fault_info表的字段结构，提供数据验证和序列化支持。
    """

    # 基础字段
    fid: str = Field(..., description="故障ID，主键")
    fault_describe: Optional[str] = Field(
        None, alias="faultDescribe", description="故障描述"
    )
    cause_describe: Optional[str] = Field(
        None, alias="causeDescribe", description="原因描述"
    )
    level: Optional[str] = Field(None, description="故障级别")

    # 时间字段
    last_update_time: Optional[datetime] = Field(
        None, alias="lastUpdateTime", description="最后更新时间，用于增量同步"
    )
    create_time: Optional[datetime] = Field(
        None, alias="createTime", description="创建时间"
    )

    # JSON字段 - 故障回放数据
    playback: Optional[str] = Field(None, description="故障回放数据，JSON格式字符串")

    # JSON字段 - 改进措施
    improve_action: Optional[str] = Field(
        None, alias="improveAction", description="改进措施，JSON格式字符串"
    )

    # HTML字段 - 问题分析（五个为什么）
    problem: Optional[str] = Field(
        None, description="问题分析，包含五个为什么逻辑树，HTML格式"
    )

    # 其他常见字段
    title: Optional[str] = Field(None, description="故障标题")
    status: Optional[str] = Field(None, description="故障状态")
    assignee: Optional[str] = Field(None, description="处理人")
    department: Optional[str] = Field(None, description="部门")
    system_name: Optional[str] = Field(None, alias="systemName", description="系统名称")
    service_name: Optional[str] = Field(
        None, alias="serviceName", description="服务名称"
    )
    occurrence_time: Optional[datetime] = Field(
        None, alias="occurrenceTime", description="故障发生时间"
    )
    resolution_time: Optional[datetime] = Field(
        None, alias="resolutionTime", description="故障解决时间"
    )
    impact_scope: Optional[str] = Field(
        None, alias="impactScope", description="影响范围"
    )
    root_cause: Optional[str] = Field(None, alias="rootCause", description="根本原因")
    solution: Optional[str] = Field(None, description="解决方案")

    model_config = {
        "validate_assignment": True,
        "populate_by_name": True,
        "json_schema_extra": {
            "example": {
                "fid": "FAULT_2024_001",
                "faultDescribe": "系统响应时间过长，用户反馈页面加载缓慢",
                "causeDescribe": "数据库查询未建立索引，导致查询效率低下",
                "level": "P2",
                "lastUpdateTime": "2024-01-15T10:30:00",
                "title": "系统性能故障",
                "status": "已解决",
                "systemName": "用户管理系统",
            }
        },
    }

    @field_validator("playback", mode="before")
    @classmethod
    def validate_playback(cls, v):
        """验证playback字段是否为有效的JSON字符串."""
        if v is None or v == "":
            return None
        if isinstance(v, dict):
            return json.dumps(v, ensure_ascii=False)
        if isinstance(v, str):
            try:
                # 验证是否为有效JSON
                json.loads(v)
                return v
            except json.JSONDecodeError:
                logger.warning(f"playback字段不是有效的JSON格式: {v[:100]}")
                return v  # 保持原值，不强制转换
        return str(v)

    @field_validator("improve_action", mode="before")
    @classmethod
    def validate_improve_action(cls, v):
        """验证improve_action字段是否为有效的JSON字符串."""
        if v is None or v == "":
            return None
        if isinstance(v, dict):
            return json.dumps(v, ensure_ascii=False)
        if isinstance(v, str):
            try:
                # 验证是否为有效JSON
                json.loads(s=v)
                return v
            except json.JSONDecodeError:
                logger.warning(f"improve_action字段不是有效的JSON格式: {v[:100]}")
                return v  # 保持原值，不强制转换
        return str(v)

    @field_validator("problem", mode="before")
    @classmethod
    def validate_problem(cls, v):
        """验证和预处理problem字段（HTML格式）."""
        if v is None or v == "":
            return None
        return str(v).strip()

    def to_text_representation(self) -> str:
        """将故障信息转换为用于向量化的文本表示.

        按照重要性顺序组织文本，用于生成向量嵌入。

        Returns:
            格式化的文本字符串
        """
        text_parts = []

        # 1. 标题和基础描述（最重要）
        if self.title:
            text_parts.append(f"故障标题：{self.title}")

        if self.fault_describe:
            text_parts.append(f"故障描述：{self.fault_describe}")

        if self.cause_describe:
            text_parts.append(f"原因描述：{self.cause_describe}")

        # 2. 系统和服务信息
        if self.system_name:
            text_parts.append(f"系统：{self.system_name}")

        if self.service_name:
            text_parts.append(f"服务：{self.service_name}")

        # 3. 影响和级别
        if self.level:
            text_parts.append(f"故障级别：{self.level}")

        if self.impact_scope:
            text_parts.append(f"影响范围：{self.impact_scope}")

        # 4. 根本原因和解决方案
        if self.root_cause:
            text_parts.append(f"根本原因：{self.root_cause}")

        if self.solution:
            text_parts.append(f"解决方案：{self.solution}")

        # 5. 问题分析（HTML转文本）
        if self.problem:
            problem_text = self._extract_text_from_html(self.problem)
            if problem_text:
                text_parts.append(f"问题分析：{problem_text}")

        # 6. JSON字段转文本, 这两个是比较大的文本
        if self.playback:
            playback_text = self._json_to_text(self.playback, "故障回放")
            if playback_text[:100]:
                text_parts.append(playback_text)

        if self.improve_action:
            improve_text = self._json_to_text(self.improve_action, "改进措施")
            if improve_text[:100]:
                text_parts.append(improve_text)

        # 7. 时间信息
        if self.occurrence_time:
            text_parts.append(
                f"发生时间：{self.occurrence_time.strftime('%Y-%m-%d %H:%M:%S')}"
            )

        # 8. 故障ID（用于关联）
        text_parts.append(f"故障ID：{self.fid}")

        return "\n".join(text_parts)

    def _extract_text_from_html(self, html_content: str) -> str:
        """从HTML内容中提取文本，特别处理五个为什么逻辑树.

        Args:
            html_content: HTML格式的内容

        Returns:
            提取的纯文本
        """
        if not html_content:
            return ""

        try:
            # 简单的HTML标签移除（可以后续使用BeautifulSoup优化）
            import re

            # 移除HTML标签
            text = re.sub(r"<[^>]+>", "", html_content)

            # 移除多余的空白字符
            text = re.sub(r"\s+", " ", text).strip()

            # 处理常见的HTML实体
            html_entities = {
                "&nbsp;": " ",
                "&lt;": "<",
                "&gt;": ">",
                "&amp;": "&",
                "&quot;": '"',
                "&#39;": "'",
            }

            for entity, char in html_entities.items():
                text = text.replace(entity, char)

            return text

        except Exception as e:
            logger.warning(f"HTML文本提取失败: {e}")
            return html_content

    def _json_to_text(self, json_str: str, prefix: str) -> str:
        """将JSON字符串转换为可读文本.

        Args:
            json_str: JSON格式字符串
            prefix: 文本前缀

        Returns:
            格式化的文本
        """
        if not json_str:
            return ""

        try:
            data = json.loads(json_str)
            text_parts = [f"{prefix}："]

            def extract_values(obj, depth=0):
                """递归提取JSON中的值."""
                if depth > 3:  # 限制递归深度
                    return

                if isinstance(obj, dict):
                    for key, value in obj.items():
                        if isinstance(value, (str, int, float)) and str(value).strip():
                            text_parts.append(f"  {key}: {value}")
                        elif isinstance(value, (dict, list)):
                            extract_values(value, depth + 1)
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        if isinstance(item, (str, int, float)) and str(item).strip():
                            text_parts.append(f"  {i + 1}. {item}")
                        elif isinstance(item, (dict, list)):
                            extract_values(item, depth + 1)

            extract_values(data)
            return "\n".join(text_parts) if len(text_parts) > 1 else ""

        except json.JSONDecodeError:
            # 如果不是有效JSON，直接使用原始文本
            return f"{prefix}：{json_str}"
        except Exception as e:
            logger.warning(f"JSON转文本失败: {e}")
            return f"{prefix}：{json_str}"

    def get_metadata(self) -> Dict[str, Any]:
        """获取用于向量存储的元数据.

        Returns:
            包含关键信息的元数据字典
        """
        metadata = {
            "fid": self.fid,
            "level": self.level,
            "status": self.status,
            "system_name": self.system_name,
            "service_name": self.service_name,
        }

        # 添加时间信息
        if self.last_update_time:
            metadata["last_update_time"] = self.last_update_time.isoformat()

        if self.occurrence_time:
            metadata["occurrence_time"] = self.occurrence_time.isoformat()

        # 移除None值
        return {k: v for k, v in metadata.items() if v is not None}

    def get_summary(self, max_length: int = 200) -> str:
        """获取故障的简要摘要.

        Args:
            max_length: 摘要最大长度

        Returns:
            简要摘要文本
        """
        summary_parts = []

        if self.title:
            summary_parts.append(self.title)
        elif self.fault_describe:
            summary_parts.append(self.fault_describe[:100])

        if self.system_name:
            summary_parts.append(f"[{self.system_name}]")

        if self.level:
            summary_parts.append(f"级别:{self.level}")

        summary = " ".join(summary_parts)

        if len(summary) > max_length:
            summary = summary[: max_length - 3] + "..."

        return summary

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "FaultInfo":
        """从字典创建FaultInfo实例.

        Args:
            data: 包含故障信息的字典

        Returns:
            FaultInfo实例
        """
        # 处理日期时间字段
        datetime_fields = [
            "lastUpdateTime",
            "createTime",
            "occurrenceTime",
            "resolutionTime",
        ]

        for field in datetime_fields:
            if field in data and data[field] is not None:
                if isinstance(data[field], str):
                    try:
                        data[field] = datetime.fromisoformat(
                            data[field].replace("Z", "+00:00")
                        )
                    except ValueError:
                        logger.warning(f"日期时间格式解析失败: {field}={data[field]}")
                        data[field] = None

        return cls(**data)


class FaultSearchResult(BaseModel):
    """故障搜索结果模型."""

    fault_info: FaultInfo = Field(..., description="故障信息")
    similarity_score: float = Field(..., description="相似度分数", ge=0.0, le=1.0)
    matched_content: str = Field(..., description="匹配的内容片段")
