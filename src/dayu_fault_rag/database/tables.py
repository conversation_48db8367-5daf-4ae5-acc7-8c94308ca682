"""数据库ORM模型定义.

使用SQLAlchemy ORM定义数据库模型，专为只读的RAG故障检索系统设计。
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import JSON, DateTime, Integer, String, Text
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


# ORM基类 - 使用SQLAlchemy 2.0的新方式
class Base(DeclarativeBase):
    pass


class FaultInfoORM(Base):
    """故障信息ORM模型.

    专为RAG系统的只读查询设计，对应fault_info表。
    根据实际数据库表结构定义字段。
    """

    __tablename__ = "fault_info"

    id: Mapped[int] = mapped_column(
        Integer, primary_key=True, autoincrement=True, comment="主键ID"
    )
    fid: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="故障ID"
    )
    faultDescribe: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="故障描述"
    )
    meetingMinutes: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="会议纪要"
    )
    currentProgress: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="当前进展"
    )
    faultYear: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="故障年份"
    )
    faultMonth: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="故障月份"
    )
    faultQuarter: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="故障季度"
    )
    startTime: Mapped[Optional[datetime]] = mapped_column(
        DateTime(6), nullable=True, comment="开始时间"
    )
    discoveryTime: Mapped[Optional[datetime]] = mapped_column(
        DateTime(6), nullable=True, comment="发现时间"
    )
    discoveryConsuming: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, comment="发现耗时"
    )
    responseTime: Mapped[Optional[datetime]] = mapped_column(
        DateTime(6), nullable=True, comment="响应时间"
    )
    responseConsuming: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, comment="响应耗时"
    )
    loacteTime: Mapped[Optional[datetime]] = mapped_column(
        DateTime(6), nullable=True, comment="定位时间"
    )
    loacteConsuming: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, comment="定位耗时"
    )
    recoverTime: Mapped[Optional[datetime]] = mapped_column(
        DateTime(6), nullable=True, comment="恢复时间"
    )
    recoverConsuming: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, comment="恢复耗时"
    )
    duration: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, comment="持续时间"
    )
    playback: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="故障回放(JSON)"
    )
    causeClassify: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="原因分类"
    )
    isChange: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否变更"
    )
    infrastructure: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="基础设施"
    )
    otherReason: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="其他原因(JSON)"
    )
    causeDescribe: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, comment="原因描述(longtext)"
    )
    serviceClassification: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="服务分类"
    )
    isEffectProduce: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="是否影响生产(JSON)"
    )
    directEffectRevenue: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="直接影响收入"
    )
    isGrade: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否分级"
    )
    level: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="故障级别"
    )
    effectContent: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, comment="影响内容(longtext)"
    )
    isEffectOutside: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否影响外部"
    )
    hasWatch: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否有监控"
    )
    hasAlert: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否有告警"
    )
    isFirstLaunch: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否首次发布"
    )
    firstLaunchSource: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="首次发布来源(JSON)"
    )
    feedbackSource: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="反馈来源"
    )
    causeLocator: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="原因定位"
    )
    isDevLocate: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否开发定位"
    )
    isOpsLocate: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否运维定位"
    )
    causeLocateTool: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="原因定位工具(JSON)"
    )
    improveAction: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="改进措施(JSON)"
    )
    handler: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="处理人(JSON)"
    )
    driver: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="驱动"
    )
    recorder: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="记录人"
    )
    simpleReportTime: Mapped[Optional[datetime]] = mapped_column(
        DateTime(6), nullable=True, comment="简单报告时间"
    )
    simpleReportDelay: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="简单报告延迟"
    )
    preparationTime: Mapped[Optional[datetime]] = mapped_column(
        DateTime(6), nullable=True, comment="准备时间"
    )
    replayTime: Mapped[Optional[datetime]] = mapped_column(
        DateTime(6), nullable=True, comment="回放时间"
    )
    replayDelay: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="回放延迟"
    )
    finishFileTime: Mapped[Optional[datetime]] = mapped_column(
        DateTime(6), nullable=True, comment="完成文件时间"
    )
    finishFileDelay: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="完成文件延迟"
    )
    recoveryPlan: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="恢复计划"
    )
    creator: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="创建人"
    )
    creatorID: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="创建人ID"
    )
    stage: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="阶段"
    )
    reportType: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="报告类型"
    )
    architectureDiagram: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="架构图(JSON)"
    )
    troubleshooting: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="故障排除(JSON)"
    )
    isOperation: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否运维"
    )
    problem: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, comment="问题分析(mediumtext)"
    )
    groupLink: Mapped[Optional[str]] = mapped_column(
        String(2048), nullable=True, comment="群组链接"
    )
    hasSLA: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否有SLA"
    )
    isEffectSLA: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否影响SLA"
    )
    declineSLA: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, comment="下降SLA"
    )
    slaDetail: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="SLA详情(JSON)"
    )
    effectSLO: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="影响SLO(JSON)"
    )
    budgetSLA: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True, comment="预算SLA"
    )
    isReplay: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="是否回放"
    )
    participants: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="参与者(JSON)"
    )
    improvementFinishTime: Mapped[Optional[datetime]] = mapped_column(
        DateTime(6), nullable=True, comment="改进完成时间"
    )
    lastUpdateTime: Mapped[Optional[datetime]] = mapped_column(
        DateTime(6), nullable=True, comment="最后更新时间"
    )
    lastUpdateUser: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="最后更新用户"
    )
    levelBasis: Mapped[Optional[str]] = mapped_column(
        String(256), nullable=True, comment="级别依据"
    )

    def __repr__(self) -> str:
        return f"<FaultInfoORM(fid='{self.fid}', level='{self.level}')>"
