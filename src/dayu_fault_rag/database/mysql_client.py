"""MySQL数据库ORM客户端实现.

使用SQLAlchemy ORM提供安全的数据库查询操作，专为只读的RAG故障检索系统设计。
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional
from urllib.parse import quote_plus

from sqlalchemy import and_, func, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

from ..config.settings import DatabaseConfig
from ..models.fault import FaultInfo
from .tables import FaultInfoORM

logger = logging.getLogger(__name__)


class MySQLClient:
    """基于ORM的MySQL数据库客户端.

    使用SQLAlchemy ORM提供类型安全的查询操作，专为RAG系统的只读场景优化。
    """

    def __init__(self, config: DatabaseConfig):
        """初始化MySQL ORM客户端.

        Args:
            config: 数据库配置，包含连接信息和过滤条件
        """
        self.config: DatabaseConfig = config
        self._engine = None
        self._session_factory = None

    async def initialize(self) -> None:
        """初始化数据库引擎和会话工厂."""
        try:
            # 构建异步数据库连接URL，正确处理特殊字符
            username = quote_plus(self.config.username)
            password = quote_plus(self.config.password)
            host = self.config.host
            port = self.config.port
            database = self.config.database
            charset = self.config.charset

            database_url = (
                f"mysql+aiomysql://{username}:{password}"
                f"@{host}:{port}/{database}"
                f"?charset={charset}&autocommit=true"
            )

            # 创建异步引擎
            self._engine = create_async_engine(
                database_url,
                pool_size=self.config.pool_size,
                max_overflow=10,
                pool_pre_ping=True,  # 连接池健康检查
                echo=False,  # 生产环境不输出SQL
            )

            # 创建会话工厂
            self._session_factory = async_sessionmaker(
                bind=self._engine,
                class_=AsyncSession,
                expire_on_commit=False,
            )

            logger.info(f"MySQL ORM客户端初始化成功，连接到 {host}:{port}/{database}")
            logger.info(
                f"数据过滤条件: reportType='{self.config.report_type_filter}', stage='{self.config.stage_filter}'"
            )
        except Exception as e:
            logger.error(f"MySQL ORM客户端初始化失败: {e}")
            raise

    async def close(self) -> None:
        """关闭数据库引擎."""
        if self._engine:
            await self._engine.dispose()
            logger.info("MySQL ORM客户端已关闭")

    def _get_session(self) -> AsyncSession:
        """获取数据库会话.

        Returns:
            异步数据库会话

        Raises:
            RuntimeError: 如果客户端未初始化
        """
        if not self._session_factory:
            raise RuntimeError("数据库客户端未初始化，请先调用 initialize()")
        return self._session_factory()

    def _build_base_query(self) -> Any:
        """构建基础查询，应用配置的过滤条件. 过滤出stage为已发布，reportType为故障的记录

        Returns:
            SQLAlchemy查询对象，已应用reportType和stage过滤
        """
        query = select(FaultInfoORM)

        # 应用配置的过滤条件
        filters = []
        if self.config.report_type_filter:
            filters.append(FaultInfoORM.reportType == self.config.report_type_filter)
        if self.config.stage_filter:
            filters.append(FaultInfoORM.stage == self.config.stage_filter)

        if filters:
            query = query.where(and_(*filters))

        return query

    def _orm_to_fault_info(self, orm_obj: FaultInfoORM) -> FaultInfo:
        """将ORM对象转换为数据模型对象.

        Args:
            orm_obj: ORM查询结果对象

        Returns:
            故障信息数据模型对象
        """
        # 构建字段映射，只包含FaultInfo模型中存在的字段
        lastUpdateTime = orm_obj.lastUpdateTime
        if lastUpdateTime:
            lastUpdateTime = lastUpdateTime.strftime("%Y-%m-%d %H:%M:%S")
        else:
            lastUpdateTime = "1999-07-14 17:21:21"
        
        data = {
            "fid": orm_obj.fid,
            "faultDescribe": orm_obj.faultDescribe,  # alias映射
            "causeDescribe": orm_obj.causeDescribe,  # 原因描述
            "level": orm_obj.level,
            "lastUpdateTime": lastUpdateTime,  # alias映射
            "playback": orm_obj.playback,
            "improveAction": orm_obj.improveAction,  # 改进措施
            "problem": orm_obj.problem,  # 问题分析
        }

        # 移除None值字段，让pydantic使用默认值
        data = {k: v for k, v in data.items() if v is not None}

        return FaultInfo(**data)

    async def get_fault_by_fid(self, fid: str) -> Optional[FaultInfo]:
        """根据FID获取故障信息.

        Args:
            fid: 故障ID

        Returns:
            故障信息对象，如果未找到则返回None
        """
        try:
            async with self._get_session() as session:
                query = self._build_base_query().where(FaultInfoORM.fid == fid)
                result = await session.execute(query)
                orm_obj = result.scalar_one_or_none()

                if orm_obj:
                    return self._orm_to_fault_info(orm_obj)
                return None

        except SQLAlchemyError as e:
            logger.error(f"查询故障信息失败，FID: {fid}，错误: {e}")
            raise
        except Exception as e:
            logger.error(f"查询故障信息时发生未预期错误，FID: {fid}，错误: {e}")
            raise

    async def get_faults_by_filter(
        self,
        level: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[FaultInfo]:
        """根据条件过滤获取故障信息列表.

        Args:
            level: 故障级别
            start_time: 开始时间
            end_time: 结束时间
            limit: 限制返回数量
            offset: 偏移量

        Returns:
            故障信息列表
        """
        try:
            async with self._get_session() as session:
                query = self._build_base_query()

                # 添加额外过滤条件
                filters = []
                if level:
                    filters.append(FaultInfoORM.level == level)
                if start_time:
                    filters.append(FaultInfoORM.lastUpdateTime >= start_time)
                if end_time:
                    filters.append(FaultInfoORM.lastUpdateTime <= end_time)

                if filters:
                    query = query.where(and_(*filters))

                # 添加排序、分页
                query = query.order_by(FaultInfoORM.lastUpdateTime.desc())
                query = query.limit(limit).offset(offset)

                result = await session.execute(query)
                orm_objects = result.scalars().all()

                return [self._orm_to_fault_info(obj) for obj in orm_objects]

        except SQLAlchemyError as e:
            logger.error(f"根据条件查询故障信息失败，错误: {e}")
            raise
        except Exception as e:
            logger.error(f"根据条件查询故障信息时发生未预期错误，错误: {e}")
            raise

    async def get_faults_by_last_update_time(
        self, last_update_time: datetime, limit: int = 1000
    ) -> List[FaultInfo]:
        """获取指定时间之后更新的故障信息.

        Args:
            last_update_time: 最后更新时间
            limit: 限制返回数量

        Returns:
            故障信息列表
        """
        try:
            async with self._get_session() as session:
                query = (
                    self._build_base_query()
                    .where(FaultInfoORM.lastUpdateTime > last_update_time)
                    .order_by(FaultInfoORM.lastUpdateTime.asc())
                    .limit(limit)
                )

                result = await session.execute(query)
                orm_objects = result.scalars().all()

                return [self._orm_to_fault_info(obj) for obj in orm_objects]

        except SQLAlchemyError as e:
            logger.error(f"查询增量故障信息失败，错误: {e}")
            raise
        except Exception as e:
            logger.error(f"查询增量故障信息时发生未预期错误，错误: {e}")
            raise

    async def get_total_fault_count(self) -> int:
        """获取符合过滤条件的故障总数.

        Returns:
            故障总数
        """
        try:
            async with self._get_session() as session:
                query = select(func.count()).select_from(FaultInfoORM)

                # 应用配置的过滤条件
                filters = []
                if self.config.report_type_filter:
                    filters.append(
                        FaultInfoORM.reportType == self.config.report_type_filter
                    )
                if self.config.stage_filter:
                    filters.append(FaultInfoORM.stage == self.config.stage_filter)

                if filters:
                    query = query.where(and_(*filters))

                result = await session.execute(query)
                count = result.scalar()
                return count or 0

        except SQLAlchemyError as e:
            logger.error(f"查询故障总数失败，错误: {e}")
            raise
        except Exception as e:
            logger.error(f"查询故障总数时发生未预期错误，错误: {e}")
            raise

    async def get_latest_update_time(self) -> Optional[datetime]:
        """获取最新的更新时间.

        Returns:
            最新更新时间，如果没有数据则返回None
        """
        try:
            async with self._get_session() as session:
                query = select(func.max(FaultInfoORM.lastUpdateTime))

                # 应用配置的过滤条件
                filters = []
                if self.config.report_type_filter:
                    filters.append(
                        FaultInfoORM.reportType == self.config.report_type_filter
                    )
                if self.config.stage_filter:
                    filters.append(FaultInfoORM.stage == self.config.stage_filter)

                if filters:
                    query = query.where(and_(*filters))

                result = await session.execute(query)
                latest_time = result.scalar()
                return latest_time

        except SQLAlchemyError as e:
            logger.error(f"查询最新更新时间失败，错误: {e}")
            raise
        except Exception as e:
            logger.error(f"查询最新更新时间时发生未预期错误，错误: {e}")
            raise

    async def health_check(self) -> bool:
        """数据库健康检查.

        Returns:
            True表示数据库连接正常，False表示异常
        """
        try:
            async with self._get_session() as session:
                # 简单的健康检查查询
                query = select(1)
                await session.execute(query)
                return True

        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}", exc_info=True)
            return False

    async def get_fault_by_id(self, fault_id: str) -> Optional[FaultInfo]:
        """根据故障ID获取单个故障信息.

        Args:
            fault_id: 故障ID

        Returns:
            故障信息，如果不存在则返回None
        """
        try:
            async with self._get_session() as session:
                query = self._build_base_query().where(FaultInfoORM.fid == fault_id)
                
                result = await session.execute(query)
                orm_object = result.scalar_one_or_none()
                
                if orm_object:
                    return self._orm_to_fault_info(orm_object)
                return None

        except SQLAlchemyError as e:
            logger.error(f"查询故障信息失败，故障ID: {fault_id}, 错误: {e}")
            raise
        except Exception as e:
            logger.error(f"查询故障信息时发生未预期错误，故障ID: {fault_id}, 错误: {e}")
            raise

    def get_filter_info(self) -> Dict[str, Any]:
        """获取当前的过滤配置信息.

        Returns:
            包含过滤条件的字典
        """
        return {
            "table_name": self.config.table_name,
            "report_type_filter": self.config.report_type_filter,
            "stage_filter": self.config.stage_filter,
        }
