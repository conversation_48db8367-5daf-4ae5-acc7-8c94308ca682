"""Milvus向量数据库客户端

基于langchain-milvus的简化实现，遵循最佳实践。
"""

from functools import cache

from langchain_core.embeddings import Embeddings
from langchain_milvus import Milvus

from dayu_fault_rag.services.embedding_service import Qwen3Embedding
from src.dayu_fault_rag.config.settings import get_settings


@cache
def get_milvus_client(
    embedding_function: Embeddings,
) -> Milvus:
    settings = get_settings()
    config = settings.milvus_config
    """获取Milvus客户端."""
    connection_args = {
        "uri": config.uri,
        "token": f"{config.username}:{config.password}",
        "db_name": config.database,
        "timeout": 30,
    }
    return Milvus(
        embedding_function=embedding_function,
        connection_args=connection_args,
        collection_name=config.collection_name,
        index_params=config.index_params,
        search_params=config.search_params,
    )


if __name__ == "__main__":
    settings = get_settings()
    from src.dayu_fault_rag.services.embedding_service import create_embeddings

    embedding_function: Qwen3Embedding = create_embeddings()
    client = get_milvus_client(
        embedding_function=embedding_function,
        config=settings.milvus_config,
    )
    client.connect()
