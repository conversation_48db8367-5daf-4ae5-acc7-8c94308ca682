# serve_as_mcp.py
from fastmcp import FastMCP

from src.dayu_fault_rag.agent.workflow import FaultSearchState, graph

mcp = FastMCP("大禹故障分析工具")


@mcp.tool()
async def run_langgraph(group_msgs: str) -> str:
    """
    大禹故障分析工具, 根据群聊消息, 给出故障分析结果和相关故障信息

    Args:
        group_msgs: 用户的故障描述或问题

    Returns:
        包含故障分析结果和重新排序的相关信息
    """
    state = FaultSearchState(msgs=group_msgs)
    final_state: FaultSearchState = await graph.ainvoke(state)
    return final_state["final_results"], final_state["reranked_results"]
