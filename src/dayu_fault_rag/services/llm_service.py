from functools import lru_cache

from langchain.chat_models import init_chat_model
from langchain_core.language_models import BaseChatModel

from src.dayu_fault_rag.config.settings import get_settings


@lru_cache
def get_llm() -> BaseChatModel:
    """获取LLM模型."""
    settings = get_settings()
    return init_chat_model(
        model=settings.llm_config.model,
        model_provider=settings.llm_config.provider,
        extra_body=settings.llm_config.extra_body,
        **settings.llm_config.external_args,
    )
