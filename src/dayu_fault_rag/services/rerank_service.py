from typing import Any, Optional, Sequence

import requests
from langchain.callbacks.base import Callbacks
from langchain.retrievers.document_compressors.base import BaseDocumentCompressor
from langchain_core.documents import Document
from pydantic import ConfigDict, Field


class Qwen3Rerank(BaseDocumentCompressor):
    # TODO 暂时还没用上, 不着急, 后面可以需要的时候再用, 还有配置还没有加上, 现在上写死的
    client: Any = None
    """RankLLM client to use for compressing documents"""
    url: str = Field(default="http://ray.ttyuyin.com:10001/ray-qwen3-emb")
    top_n: int = Field(default=10)
    """Top N documents to return."""
    model: str = Field(
        default="Qwen/Qwen3-Reranker-4B", description="qwen3_rerank model"
    )
    """Name of model to use for reranking."""

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        extra="forbid",
    )

    def compress_documents(
        self,
        documents: Sequence[Document],
        query: str,
        callbacks: Optional[Callbacks] = None,
    ) -> Sequence[Document]:
        """使用qwen3-reranker 对文档进行重排序，支持分批处理"""

        if not documents:
            return []

        # 准备API请求URL
        url = f"{self.url}/v1/reranker"

        # 分批处理文档，每批10个
        batch_size = 10
        all_ranked_documents = []

        for i in range(0, len(documents), batch_size):
            # 获取当前批次的文档
            batch_documents = documents[i : i + batch_size]
            batch_doc_contents = [doc.page_content for doc in batch_documents]

            # 为当前批次准备payload
            payload = {
                "model": self.model,
                "query": query,
                "documents": batch_doc_contents,
                "top_n": len(batch_doc_contents),  # 批次内不限制数量
                "return_documents": False,  # 我们只需要索引顺序
            }

            try:
                # 发送请求到reranker API
                response = requests.post(url, json=payload)
                response.raise_for_status()  # 如果请求失败则抛出异常

                # 获取排序后的索引和分数
                ranked_indices = [
                    (item["index"], item["relevance_score"])
                    for item in response.json()["result"]
                ]

                # 将批次内的文档添加relevance_score并收集
                for batch_idx, score in ranked_indices:
                    doc = batch_documents[batch_idx]
                    doc.metadata["relevance_score"] = score
                    all_ranked_documents.append(doc)

            except Exception as e:
                # 如果某个批次失败，记录错误但继续处理其他批次
                print(f"批次 {i // batch_size + 1} rerank失败: {e}")
                # 将当前批次的文档添加默认分数0.0
                for doc in batch_documents:
                    doc.metadata["relevance_score"] = 0.0
                    all_ranked_documents.append(doc)

        # 根据所有文档的relevance_score进行全局排序
        all_ranked_documents.sort(
            key=lambda x: x.metadata.get("relevance_score", 0.0), reverse=True
        )

        # 返回top_n个文档
        return all_ranked_documents[: self.top_n]


if __name__ == "__main__":
    reranker = Qwen3Rerank()
    documents = [
        Document(page_content="我想去广州", metadata={"source": "source2"}),
        Document(page_content="Hello, world!", metadata={"source": "source1"}),
    ]
    query = "你好世界!"
    ranked_documents = reranker.compress_documents(documents, query)
    print(ranked_documents)
