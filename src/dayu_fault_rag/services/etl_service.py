"""ETL数据处理服务

实现从MySQL抽取故障数据，转换为向量化文档，并加载到Milvus向量数据库的完整ETL流程。
"""

import asyncio
import json
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from langchain_milvus import Milvus
from langchain_text_splitters import RecursiveCharacterTextSplitter

from ..config.settings import ETLConfig, get_settings
from ..database.milvus_client import get_milvus_client
from ..database.mysql_client import MySQLClient
from ..models.fault import FaultInfo
from ..models.vector_document import FaultDocument
from ..services.embedding_service import EmbeddingService
from ..utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class ETLWatermark:
    """ETL水印数据结构."""

    last_update_time: Optional[datetime] = None
    last_processed_fid: Optional[str] = None
    total_processed: int = 0
    last_run_time: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式."""
        return {
            "last_update_time": self.last_update_time.isoformat()
            if self.last_update_time
            else None,
            "last_processed_fid": self.last_processed_fid,
            "total_processed": self.total_processed,
            "last_run_time": self.last_run_time.isoformat()
            if self.last_run_time
            else None,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ETLWatermark":
        """从字典创建水印实例."""
        last_update_time = None
        if data.get("last_update_time"):
            try:
                last_update_time = datetime.fromisoformat(data["last_update_time"])
            except ValueError:
                logger.warning(f"无效的时间格式: {data['last_update_time']}")

        last_run_time = None
        if data.get("last_run_time"):
            try:
                last_run_time = datetime.fromisoformat(data["last_run_time"])
            except ValueError:
                logger.warning(f"无效的时间格式: {data['last_run_time']}")

        return cls(
            last_update_time=last_update_time,
            last_processed_fid=data.get("last_processed_fid"),
            total_processed=data.get("total_processed", 0),
            last_run_time=last_run_time,
        )


@dataclass
class ETLStats:
    """ETL统计信息."""

    extracted_count: int = 0
    transformed_count: int = 0
    loaded_count: int = 0
    skipped_count: int = 0
    error_count: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None

    def get_duration(self) -> float:
        """获取处理耗时（秒）."""
        if self.start_time and self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式."""
        return {
            "extracted_count": self.extracted_count,
            "transformed_count": self.transformed_count,
            "loaded_count": self.loaded_count,
            "skipped_count": self.skipped_count,
            "error_count": self.error_count,
            "duration_seconds": self.get_duration(),
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
        }


class ETLService:
    """ETL数据处理服务.

    负责从MySQL抽取故障数据，转换为向量化文档，并加载到Milvus向量数据库。
    """

    def __init__(
        self,
        mysql_client: MySQLClient,
        milvus_client: Milvus,
        embedding_service: EmbeddingService,
        config: Optional[ETLConfig] = None,
    ):
        """初始化ETL服务.

        Args:
            mysql_client: MySQL数据库客户端
            milvus_client: Milvus向量数据库客户端
            embedding_service: 向量化服务
            config: ETL配置，如果为None则使用默认配置
        """
        self.mysql_client = mysql_client
        self.milvus_client: Milvus = milvus_client
        self.embedding_service = embedding_service
        self.config = config or get_settings().etl_config

        # 文本分割器
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap,
            separators=["\n\n", "\n", "。", "；", "！", "？", ".", ";", "!", "?", " "],
            keep_separator=True,
        )

        # 水印文件路径
        self.watermark_file = Path(self.config.watermark_file)

        logger.info(f"ETL服务初始化完成，水印文件: {self.watermark_file}")

    async def run_full_etl(self) -> ETLStats:
        """执行全量ETL处理.

        Returns:
            ETL统计信息
        """
        logger.info("开始执行全量ETL处理")
        stats = ETLStats(start_time=datetime.now())

        try:
            # 1. 数据抽取
            faults: List[FaultInfo] = await self.extract_all_faults()
            stats.extracted_count = len(faults)
            logger.info(f"抽取到 {stats.extracted_count} 条故障数据")

            # 2. 数据转换和加载
            await self._process_faults_batch(faults, stats)

            # 3. 更新水印
            if faults:
                latest_fault = max(
                    faults, key=lambda f: f.last_update_time or datetime.min
                )
                await self._update_watermark(
                    last_update_time=latest_fault.last_update_time,
                    last_processed_fid=latest_fault.fid,
                    total_processed=stats.loaded_count,
                )

            stats.end_time = datetime.now()
            logger.info(f"全量ETL处理完成: {stats.to_dict()}")
            return stats

        except Exception as e:
            stats.end_time = datetime.now()
            logger.error(f"全量ETL处理失败: {e}")
            raise

    async def run_incremental_etl(self) -> ETLStats:
        """执行增量ETL处理.

        Returns:
            ETL统计信息
        """
        logger.info("开始执行增量ETL处理")
        stats = ETLStats(start_time=datetime.now())

        try:
            # 1. 读取水印
            watermark = await self._load_watermark()
            logger.info(f"当前水印: {watermark.to_dict()}")

            # 2. 数据抽取（增量）
            faults = await self.extract_incremental_faults(watermark.last_update_time)
            stats.extracted_count = len(faults)
            logger.info(f"抽取到 {stats.extracted_count} 条增量故障数据")

            if not faults:
                logger.info("没有新的故障数据需要处理")
                stats.end_time = datetime.now()
                return stats

            # 3. 数据转换和加载
            await self._process_faults_batch(faults, stats)

            # 4. 更新水印
            latest_fault = max(faults, key=lambda f: f.last_update_time or datetime.min)
            await self._update_watermark(
                last_update_time=latest_fault.last_update_time,
                last_processed_fid=latest_fault.fid,
                total_processed=watermark.total_processed + stats.loaded_count,
            )

            stats.end_time = datetime.now()
            logger.info(f"增量ETL处理完成: {stats.to_dict()}")
            return stats

        except Exception as e:
            stats.end_time = datetime.now()
            logger.error(f"增量ETL处理失败: {e}")
            raise

    async def extract_all_faults(self) -> List[FaultInfo]:
        """抽取所有故障数据.

        Returns:
            故障信息列表
        """
        logger.info("开始抽取所有故障数据")

        try:
            # 分批抽取数据，避免内存溢出
            all_faults = []
            offset = 0
            batch_size = self.config.batch_size

            while True:
                batch_faults = await self.mysql_client.get_faults_by_filter(
                    limit=batch_size,
                    offset=offset,
                )

                if not batch_faults:
                    break

                all_faults.extend(batch_faults)
                offset += batch_size

                logger.debug(f"已抽取 {len(all_faults)} 条故障数据")

                # 防止无限循环
                if len(batch_faults) < batch_size:
                    break

            logger.info(f"全量数据抽取完成，共 {len(all_faults)} 条记录")
            return all_faults

        except Exception as e:
            logger.error(f"全量数据抽取失败: {e}")
            raise

    async def extract_incremental_faults(
        self, last_update_time: Optional[datetime]
    ) -> List[FaultInfo]:
        """抽取增量故障数据.

        Args:
            last_update_time: 上次更新时间

        Returns:
            故障信息列表
        """
        if not last_update_time:
            logger.info("没有水印时间，执行全量抽取")
            return await self.extract_all_faults()

        logger.info(f"开始抽取增量故障数据，时间戳: {last_update_time}")

        try:
            # 分批抽取增量数据
            all_faults = []
            limit = self.config.batch_size

            while True:
                batch_faults = await self.mysql_client.get_faults_by_last_update_time(
                    last_update_time=last_update_time,
                    limit=limit,
                )

                if not batch_faults:
                    break

                all_faults.extend(batch_faults)

                # 更新查询时间戳为最后一条记录的时间
                last_fault = batch_faults[-1]
                if last_fault.last_update_time:
                    last_update_time = last_fault.last_update_time

                logger.debug(f"已抽取 {len(all_faults)} 条增量故障数据")

                # 如果返回的记录数少于限制，说明已经没有更多数据
                if len(batch_faults) < limit:
                    break

            logger.info(f"增量数据抽取完成，共 {len(all_faults)} 条记录")
            return all_faults

        except Exception as e:
            logger.error(f"增量数据抽取失败: {e}")
            raise

    async def transform_fault_to_documents(
        self, fault: FaultInfo
    ) -> List[FaultDocument]:
        """将故障信息转换为向量文档.

        Args:
            fault: 故障信息

        Returns:
            向量文档列表
        """
        try:
            # 应该在这里删除, 不是在外面删除
            self.milvus_client.delete(expr=f"fid == '{fault.fid}'")
            # 1. 获取故障的文本表示
            text_content = fault.to_text_representation()

            if not text_content.strip():
                logger.warning(f"故障 {fault.fid} 没有有效文本内容")
                return []

            # 2. 文本分块处理
            chunks: List[str] = await self._split_text_into_chunks(text_content)

            if not chunks:
                logger.warning(f"故障 {fault.fid} 分块处理后没有有效内容")
                return []

            # 5. 创建向量文档
            vector_documents = []
            metadata = fault.get_metadata()
            total_chunks = len(chunks)
            i = 0
            for chunk in chunks:
                vector_doc = FaultDocument(
                    fid=fault.fid,
                    page_content=chunk,
                    metadata=metadata,
                    chunk_index=i,
                    total_chunks=total_chunks,
                    embedding_model=self.embedding_service.config.model_name,
                )
                vector_documents.append(vector_doc)
                i += 1
            logger.debug(
                f"故障 {fault.fid} 转换完成，生成 {len(vector_documents)} 个文档"
            )
            return vector_documents

        except Exception as e:
            logger.error(f"故障 {fault.fid} 转换失败: {e}")
            return []

    async def load_documents_to_milvus(self, documents: List[FaultDocument]) -> bool:
        """将向量文档批量加载到Milvus.

        Args:
            documents: 向量文档列表

        Returns:
            加载是否成功
        """
        if not documents:
            return True

        try:
            # 批量添加文档到Milvus
            doc_ids = self.milvus_client.add_documents(documents, batch_size=64)

            logger.info(f"成功加载 {len(doc_ids)} 个文档到Milvus")
            return True

        except Exception as e:
            logger.error(f"文档加载到Milvus失败: {e}", exc_info=True)
            return False

    async def _process_faults_batch(
        self, faults: List[FaultInfo], stats: ETLStats
    ) -> None:
        """批量处理故障数据.

        Args:
            faults: 故障信息列表
            stats: 统计信息对象
        """
        batch_size = self.config.batch_size

        for i in range(0, len(faults), batch_size):
            # 如果向量数据库中存在一个删掉, 重新加载

            batch = faults[i : i + batch_size]
            logger.info(f"处理第 {i // batch_size + 1} 批，共 {len(batch)} 条记录")

            # 并发处理当前批次的故障, 如果数据库里面有故障, 则删除, 换成新的故障, 通过fid区分
            tasks = [self.transform_fault_to_documents(fault) for fault in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 收集所有成功转换的文档
            all_documents = []
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    logger.error(f"故障 {batch[j].fid} 处理失败: {result}")
                    stats.error_count += 1
                elif isinstance(result, list):
                    if result:
                        all_documents.extend(result)
                        stats.transformed_count += 1
                    else:
                        stats.skipped_count += 1

            # 批量加载到Milvus
            if all_documents:
                success = await self.load_documents_to_milvus(all_documents)
                if success:
                    stats.loaded_count += len([doc for doc in all_documents])
                else:
                    stats.error_count += len(all_documents)

            logger.info(
                f"批次处理完成，已处理: {stats.transformed_count}, 已加载: {stats.loaded_count}, 错误: {stats.error_count}"
            )

    async def _split_text_into_chunks(self, text: str) -> List[str]:
        """将文本分割为文档块.

        Args:
            text: 原始文本

        Returns:
            文档块列表
        """
        try:
            # 使用RecursiveCharacterTextSplitter进行分块
            chunks = self.text_splitter.split_text(text)
            chunks = [chunk for chunk in chunks if len(chunk) > 100]

            return chunks

        except Exception as e:
            logger.error(f"文本分块失败: {e}", exc_info=True)
            return []

    async def _load_watermark(self) -> ETLWatermark:
        """加载ETL水印.

        Returns:
            ETL水印对象
        """
        try:
            if self.watermark_file.exists():
                with open(self.watermark_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                return ETLWatermark.from_dict(data)
            else:
                logger.info("水印文件不存在，创建新水印")
                return ETLWatermark()

        except Exception as e:
            logger.error(f"加载水印文件失败: {e}")
            return ETLWatermark()

    async def _save_watermark(self, watermark: ETLWatermark) -> None:
        """保存ETL水印.

        Args:
            watermark: ETL水印对象
        """
        try:
            # 确保目录存在
            self.watermark_file.parent.mkdir(parents=True, exist_ok=True)

            with open(self.watermark_file, "w", encoding="utf-8") as f:
                json.dump(watermark.to_dict(), f, ensure_ascii=False, indent=2)

            logger.debug(f"水印文件已保存: {self.watermark_file}")

        except Exception as e:
            logger.error(f"保存水印文件失败: {e}")
            raise

    async def _update_watermark(
        self,
        last_update_time: Optional[datetime] = None,
        last_processed_fid: Optional[str] = None,
        total_processed: Optional[int] = None,
    ) -> None:
        """更新ETL水印.

        Args:
            last_update_time: 最后更新时间
            last_processed_fid: 最后处理的故障ID
            total_processed: 总处理数量
        """
        try:
            watermark = await self._load_watermark()

            if last_update_time is not None:
                watermark.last_update_time = last_update_time
            if last_processed_fid is not None:
                watermark.last_processed_fid = last_processed_fid
            if total_processed is not None:
                watermark.total_processed = total_processed

            watermark.last_run_time = datetime.now()

            await self._save_watermark(watermark)
            logger.info(f"水印已更新: {watermark.to_dict()}")

        except Exception as e:
            logger.error(f"更新水印失败: {e}")
            raise

    async def get_etl_status(self) -> Dict[str, Any]:
        """获取ETL状态信息.

        Returns:
            ETL状态字典
        """
        try:
            watermark = await self._load_watermark()

            # 获取数据库统计信息
            total_faults = await self.mysql_client.get_total_fault_count()
            latest_update_time = await self.mysql_client.get_latest_update_time()

            # 获取Milvus统计信息
            milvus_stats = await self.milvus_client.get_collection_stats()

            return {
                "watermark": watermark.to_dict(),
                "mysql_stats": {
                    "total_faults": total_faults,
                    "latest_update_time": latest_update_time.isoformat()
                    if latest_update_time
                    else None,
                },
                "milvus_stats": milvus_stats,
                "embedding_stats": self.embedding_service.get_cache_stats(),
                "config": {
                    "batch_size": self.config.batch_size,
                    "chunk_size": self.config.chunk_size,
                    "sync_interval": self.config.sync_interval,
                },
            }

        except Exception as e:
            logger.error(f"获取ETL状态失败: {e}")
            return {"error": str(e)}

    async def health_check(self) -> bool:
        """ETL服务健康检查.

        Returns:
            服务是否健康
        """
        try:
            # 检查各个组件的健康状态
            mysql_healthy = await self.mysql_client.health_check()
            milvus_healthy = self.milvus_client.is_connected()
            embedding_healthy = self.embedding_service.health_check()

            is_healthy = mysql_healthy and milvus_healthy and embedding_healthy

            if is_healthy:
                logger.info("ETL服务健康检查通过")
            else:
                logger.error(
                    f"ETL服务健康检查失败: MySQL={mysql_healthy}, Milvus={milvus_healthy}, Embedding={embedding_healthy}"
                )

            return is_healthy

        except Exception as e:
            logger.error(f"ETL服务健康检查异常: {e}", exc_info=True)
            return False

    async def cleanup_old_documents(self, days: int = 30) -> int:
        """清理旧的向量文档.

        Args:
            days: 保留天数

        Returns:
            清理的文档数量
        """
        # 这个功能需要根据实际需求实现
        # 目前Milvus客户端可能不支持基于时间的批量删除
        logger.info(f"清理 {days} 天前的文档功能暂未实现")
        return 0


# 便利函数
async def create_etl_service(
    mysql_client: Optional[MySQLClient] = None,
    milvus_client: Optional[Milvus] = None,
    embedding_service: Optional[EmbeddingService] = None,
) -> ETLService:
    """创建ETL服务实例.

    Args:
        mysql_client: MySQL客户端，如果为None则创建新实例
        milvus_client: Milvus客户端，如果为None则创建新实例
        embedding_service: 向量化服务，如果为None则创建新实例

    Returns:
        ETL服务实例
    """
    settings = get_settings()

    # 创建MySQL客户端
    if mysql_client is None:
        mysql_client = MySQLClient(settings.database_config)
        await mysql_client.initialize()

    # 创建向量化服务
    if embedding_service is None:
        from .embedding_service import get_embedding_service

        embedding_service = get_embedding_service()

    # 创建Milvus客户端
    from src.dayu_fault_rag.services.embedding_service import create_embeddings

    if milvus_client is None:
        milvus_client = get_milvus_client(embedding_function=create_embeddings())

    return ETLService(
        mysql_client=mysql_client,
        milvus_client=milvus_client,
        embedding_service=embedding_service,
    )
