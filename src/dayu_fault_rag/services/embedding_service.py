"""向量化服务

基于Qwen3-Embedding-4B模型的向量化服务实现，集成<PERSON><PERSON><PERSON><PERSON>生态和缓存机制。
"""

import hashlib
import json
import time
from functools import lru_cache
from pathlib import Path
from typing import Any, Dict, List, Optional

import requests
from langchain_core.embeddings import Embeddings

from ..config.settings import EmbeddingConfig, get_settings
from ..utils.logging import get_logger

logger = get_logger(__name__)


class EmbeddingCache:
    """向量化缓存管理器.

    支持内存和磁盘缓存，提高向量化效率。
    """

    def __init__(
        self,
        cache_dir: Optional[Path] = None,
        max_memory_size: int = 1000,
        enable_disk_cache: bool = True,
    ):
        """初始化缓存管理器.

        Args:
            cache_dir: 磁盘缓存目录
            max_memory_size: 内存缓存最大条目数
            enable_disk_cache: 是否启用磁盘缓存
        """
        self.cache_dir = cache_dir or Path(".cache/embeddings")
        self.max_memory_size = max_memory_size
        self.enable_disk_cache = enable_disk_cache
        self._memory_cache: Dict[str, List[float]] = {}
        self._access_times: Dict[str, float] = {}

        if self.enable_disk_cache:
            self.cache_dir.mkdir(parents=True, exist_ok=True)

        logger.info(
            f"初始化嵌入缓存，磁盘缓存: {enable_disk_cache}, 内存缓存大小: {max_memory_size}"
        )

    def _get_cache_key(self, text: str, model_name: str, is_query: bool = False) -> str:
        """生成缓存键."""
        content = f"{model_name}:{is_query}:{text}"
        return hashlib.md5(content.encode("utf-8")).hexdigest()

    def get(
        self, text: str, model_name: str, is_query: bool = False
    ) -> Optional[List[float]]:
        """从缓存获取向量."""
        cache_key = self._get_cache_key(text, model_name, is_query)

        # 先检查内存缓存
        if cache_key in self._memory_cache:
            self._access_times[cache_key] = time.time()
            logger.debug(f"内存缓存命中: {text[:50]}...")
            return self._memory_cache[cache_key]

        # 再检查磁盘缓存
        if self.enable_disk_cache:
            cache_file = self.cache_dir / f"{cache_key}.json"
            if cache_file.exists():
                try:
                    with open(cache_file, "r", encoding="utf-8") as f:
                        data = json.load(f)

                    embedding = data["embedding"]
                    # 加载到内存缓存
                    self._put_memory_cache(cache_key, embedding)
                    logger.debug(f"磁盘缓存命中: {text[:50]}...")
                    return embedding
                except Exception as e:
                    logger.warning(f"读取磁盘缓存失败: {e}")
                    cache_file.unlink(missing_ok=True)

        return None

    def put(
        self, text: str, embedding: List[float], model_name: str, is_query: bool = False
    ):
        """存储向量到缓存."""
        cache_key = self._get_cache_key(text, model_name, is_query)

        # 存储到内存缓存
        self._put_memory_cache(cache_key, embedding)

        # 存储到磁盘缓存
        if self.enable_disk_cache:
            try:
                cache_file = self.cache_dir / f"{cache_key}.json"
                data = {
                    "text": text[:100],  # 只存储前100个字符用于调试
                    "embedding": embedding,
                    "model_name": model_name,
                    "is_query": is_query,
                    "timestamp": time.time(),
                }
                with open(cache_file, "w", encoding="utf-8") as f:
                    json.dump(data, f)
                logger.debug(f"向量已缓存: {text[:50]}...")
            except Exception as e:
                logger.warning(f"写入磁盘缓存失败: {e}")

    def _put_memory_cache(self, cache_key: str, embedding: List[float]):
        """存储到内存缓存，实现LRU策略."""
        # 如果缓存已满，移除最久未访问的项目
        if len(self._memory_cache) >= self.max_memory_size:
            oldest_key = min(
                self._access_times.keys(), key=lambda k: self._access_times[k]
            )
            del self._memory_cache[oldest_key]
            del self._access_times[oldest_key]

        self._memory_cache[cache_key] = embedding
        self._access_times[cache_key] = time.time()

    def clear(self):
        """清空所有缓存."""
        self._memory_cache.clear()
        self._access_times.clear()

        if self.enable_disk_cache and self.cache_dir.exists():
            for cache_file in self.cache_dir.glob("*.json"):
                cache_file.unlink(missing_ok=True)

        logger.info("缓存已清空")

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息."""
        disk_files = 0
        if self.enable_disk_cache and self.cache_dir.exists():
            disk_files = len(list(self.cache_dir.glob("*.json")))

        return {
            "memory_cache_size": len(self._memory_cache),
            "disk_cache_size": disk_files,
            "max_memory_size": self.max_memory_size,
            "enable_disk_cache": self.enable_disk_cache,
        }


class Qwen3Embedding(Embeddings):
    """Qwen3嵌入模型实现，集成LangChain生态."""

    def __init__(
        self,
        config: Optional[EmbeddingConfig] = None,
        cache: Optional[EmbeddingCache] = None,
        is_query: bool = False,
    ):
        """初始化Qwen3Embedding.

        Args:
            config: 嵌入配置，如果为None则使用默认配置
            cache: 缓存管理器，如果为None则创建默认缓存
            is_query: 是否用于查询（影响缓存策略）
        """
        if config is None:
            settings = get_settings()
            config = settings.embedding_config

        self.config = config
        self.is_query = is_query
        self.cache = cache or EmbeddingCache()

        # 健康检查状态
        self._last_health_check = 0
        self._health_check_interval = 300  # 5分钟检查一次
        self._is_healthy = None  # None表示未检查过

        logger.info(
            f"初始化Qwen3Embedding: {self.config.base_url}, "
            f"model={self.config.model_name}, is_query={is_query}"
        )


    def _get_embedding_from_api(self, texts: List[str]) -> List[List[float]]:
        """从API获取文本嵌入向量."""
        url = f"{self.config.base_url}/v1/embeddings"

        payload = {
            "model": self.config.model_name,
            "input": texts,
            "encoding_format": "float",
        }

        try:
            response = requests.post(
                url,
                json=payload,
                timeout=self.config.timeout,
                headers={"Content-Type": "application/json"},
            )
            response.raise_for_status()

            result = response.json()

            if "data" not in result:
                raise ValueError(f"API响应格式错误，缺少data字段: {result}")

            embeddings = [item["embedding"] for item in result["data"]]

            # 验证向量维度
            if embeddings and len(embeddings[0]) != self.get_dimension():
                logger.warning(
                    f"向量维度不匹配，期望: {self.get_dimension()}, "
                    f"实际: {len(embeddings[0])}"
                )

            logger.debug(f"成功获取 {len(texts)} 个文本的嵌入向量")
            return embeddings

        except requests.exceptions.Timeout:
            logger.error(f"嵌入向量请求超时 ({self.config.timeout}s)")
            raise
        except requests.exceptions.RequestException as e:
            logger.error(f"嵌入向量请求失败: {e}")
            raise
        except (KeyError, ValueError) as e:
            logger.error(f"嵌入向量响应解析错误: {e}")
            raise

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """嵌入文档列表，支持缓存和批量处理."""
        if not texts:
            return []

        logger.info(f"正在嵌入 {len(texts)} 个文档")

        # 检查缓存
        cached_embeddings = []
        uncached_texts = []
        uncached_indices = []

        for i, text in enumerate(texts):
            cached = self.cache.get(text, self.config.model_name, is_query=False)
            if cached is not None:
                cached_embeddings.append((i, cached))
            else:
                uncached_texts.append(text)
                uncached_indices.append(i)

        logger.debug(
            f"缓存命中: {len(cached_embeddings)}, 需要请求: {len(uncached_texts)}"
        )

        # 处理未缓存的文本
        new_embeddings = []
        if uncached_texts:
            # 批量处理
            batch_size = self.config.batch_size
            for i in range(0, len(uncached_texts), batch_size):
                batch = uncached_texts[i : i + batch_size]
                batch_embeddings = self._get_embedding_from_api(batch)

                # 存储到缓存
                for text, embedding in zip(batch, batch_embeddings):
                    self.cache.put(
                        text, embedding, self.config.model_name, is_query=False
                    )

                new_embeddings.extend(batch_embeddings)

        # 合并结果
        all_embeddings = [None] * len(texts)

        # 填入缓存的结果
        for i, embedding in cached_embeddings:
            all_embeddings[i] = embedding

        # 填入新获取的结果
        for i, embedding in zip(uncached_indices, new_embeddings):
            all_embeddings[i] = embedding
            if embedding is None:
                logger.warning(f"第 {i} 个文档的向量为空")

        for i, embedding in enumerate(all_embeddings):
            if embedding is None:
                logger.warning(f"第 {i} 个文档的向量为空")

        logger.info(
            f"文档嵌入完成，维度: {len(all_embeddings[0]) if all_embeddings else 0}"
        )
        return all_embeddings

    def embed_query(self, text: str) -> List[float]:
        """嵌入查询文本，支持缓存."""
        if not text.strip():
            raise ValueError("查询文本不能为空")

        logger.debug(f"正在嵌入查询: {text[:100]}...")

        # 检查缓存
        cached = self.cache.get(text, self.config.model_name, is_query=True)
        if cached is not None:
            logger.debug("查询缓存命中")
            return cached


        # 获取嵌入
        embeddings = self._get_embedding_from_api([text])
        embedding = embeddings[0]

        # 存储到缓存
        self.cache.put(text, embedding, self.config.model_name, is_query=True)

        logger.debug("查询嵌入完成")
        return embedding

    def get_dimension(self) -> int:
        """获取向量维度."""
        # 根据配置返回正确的维度
        # Qwen3-Embedding-4B的实际维度应该从配置中获取
        return self.config.dimension if hasattr(self.config, "dimension") else 4096


class EmbeddingService:
    """向量化服务.

    提供文档和查询的向量化功能，集成缓存和批量处理。
    """

    def __init__(
        self,
        config: Optional[EmbeddingConfig] = None,
        cache: Optional[EmbeddingCache] = None,
    ):
        """初始化向量化服务.

        Args:
            config: 嵌入配置，如果为None则使用默认配置
            cache: 缓存管理器，如果为None则创建默认缓存
        """
        if config is None:
            settings = get_settings()
            config = settings.embedding_config

        self.config = config
        self.cache = cache or EmbeddingCache()
        self._document_embedder = None
        self._query_embedder = None

        logger.info("向量化服务初始化完成")

    @property
    def document_embedder(self) -> Qwen3Embedding:
        """获取文档嵌入器（延迟初始化）."""
        if self._document_embedder is None:
            self._document_embedder = Qwen3Embedding(
                config=self.config,
                cache=self.cache,
                is_query=False,  # 文档嵌入
            )
        return self._document_embedder

    @property
    def query_embedder(self) -> Qwen3Embedding:
        """获取查询嵌入器（延迟初始化）."""
        if self._query_embedder is None:
            self._query_embedder = Qwen3Embedding(
                config=self.config,
                cache=self.cache,
                is_query=True,  # 查询嵌入
            )
        return self._query_embedder

    def embed_documents(self, documents: List[str]) -> List[List[float]]:
        """向量化文档列表.

        Args:
            documents: 文档文本列表

        Returns:
            向量列表
        """
        if not documents:
            return []

        logger.info(f"开始向量化 {len(documents)} 个文档")

        try:
            # 文本预处理
            processed_docs = []
            for doc in documents:
                # 截断过长的文本
                if len(doc) > self.config.max_length:
                    doc = doc[: self.config.max_length]
                    logger.debug(f"文档被截断到 {self.config.max_length} 字符")
                processed_docs.append(doc.strip())

            # 过滤空文档
            non_empty_docs = [(i, doc) for i, doc in enumerate(processed_docs) if doc]
            if len(non_empty_docs) < len(processed_docs):
                logger.warning(
                    f"跳过 {len(processed_docs) - len(non_empty_docs)} 个空文档"
                )

            if not non_empty_docs:
                return [[0.0] * self.get_embedding_dimension() for _ in documents]

            # 嵌入非空文档
            indices, valid_docs = zip(*non_empty_docs)
            embeddings = self.document_embedder.embed_documents(list(valid_docs))

            # 构建完整结果
            result = []
            embedding_iter = iter(zip(indices, embeddings))
            next_idx, next_embedding = next(embedding_iter, (None, None))

            for i in range(len(documents)):
                if i == next_idx:
                    result.append(next_embedding)
                    next_idx, next_embedding = next(embedding_iter, (None, None))
                else:
                    # 空文档使用零向量
                    result.append([0.0] * self.get_embedding_dimension())

            logger.info(f"文档向量化完成，共 {len(result)} 个向量")
            return result

        except Exception as e:
            logger.error(f"文档向量化失败: {e}")
            raise

    def embed_query(self, query: str) -> List[float]:
        """向量化查询文本.

        Args:
            query: 查询文本

        Returns:
            查询向量
        """
        if not query.strip():
            raise ValueError("查询文本不能为空")

        logger.debug(f"开始向量化查询: {query[:100]}...")

        try:
            # 文本预处理
            processed_query = query.strip()
            if len(processed_query) > self.config.max_length:
                processed_query = processed_query[: self.config.max_length]
                logger.debug(f"查询被截断到 {self.config.max_length} 字符")

            embedding = self.query_embedder.embed_query(processed_query)
            logger.debug("查询向量化完成")
            return embedding

        except Exception as e:
            logger.error(f"查询向量化失败: {e}")
            raise

    def get_embedding_dimension(self) -> int:
        """获取向量维度.

        Returns:
            向量维度
        """
        return self.document_embedder.get_dimension()

    def health_check(self) -> bool:
        """健康检查.

        Returns:
            服务是否正常
        """
        try:
            # 使用查询嵌入器进行健康检查
            return self.query_embedder._health_check()
        except Exception as e:
            logger.error(f"向量化服务健康检查失败: {e}", exc_info=True)
            return False

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息."""
        return self.cache.get_stats()

    def clear_cache(self):
        """清空缓存."""
        self.cache.clear()
        logger.info("向量化服务缓存已清空")

    def batch_embed_with_metadata(
        self, texts: List[str], metadata: Optional[List[Dict[str, Any]]] = None
    ) -> List[Dict[str, Any]]:
        """批量嵌入文本并返回带元数据的结果.

        Args:
            texts: 文本列表
            metadata: 元数据列表

        Returns:
            包含向量和元数据的结果列表
        """
        if metadata is None:
            metadata = [{}] * len(texts)

        if len(texts) != len(metadata):
            raise ValueError("文本和元数据列表长度不匹配")

        embeddings = self.embed_documents(texts)

        results = []
        for text, embedding, meta in zip(texts, embeddings, metadata):
            results.append({
                "text": text,
                "embedding": embedding,
                "metadata": meta,
                "dimension": len(embedding),
            })

        return results


# 便利函数
@lru_cache(maxsize=1)
def get_embedding_service() -> EmbeddingService:
    """获取全局嵌入服务实例（单例模式）."""
    return EmbeddingService()


def create_embeddings() -> Qwen3Embedding:
    """创建嵌入实例，用于LangChain集成."""
    return Qwen3Embedding()


def create_query_embeddings() -> Qwen3Embedding:
    """创建查询嵌入实例，用于LangChain集成."""
    return Qwen3Embedding(is_query=True)
