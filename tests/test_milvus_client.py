"""Milvus客户端单元测试."""

from typing import List
from unittest.mock import AsyncMock, Mock, patch

import pytest
from langchain_core.documents import Document
from langchain_core.embeddings import Embeddings

from src.dayu_fault_rag.config.settings import MilvusConfig
from src.dayu_fault_rag.database.milvus_client import MilvusClient
from src.dayu_fault_rag.models.vector_document import VectorDocument, VectorSearchResult


class MockEmbeddings(Embeddings):
    """Mock嵌入函数用于测试."""

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """嵌入文档."""
        return [[0.1, 0.2, 0.3] * 256 for _ in texts]  # 768维向量

    def embed_query(self, text: str) -> List[float]:
        """嵌入查询."""
        return [0.1, 0.2, 0.3] * 256  # 768维向量


class TestMilvusClient:
    """MilvusClient测试类."""

    @pytest.fixture
    def milvus_config(self):
        """Milvus配置fixture."""
        return MilvusConfig(
            host="localhost",
            port=19530,
            username="test_user",
            password="test_password",
            collection_name="test_fault_vectors",
            dimension=2560,
            metric_type="COSINE",
        )

    @pytest.fixture
    def mock_embeddings(self):
        """Mock嵌入函数fixture."""
        return MockEmbeddings()

    @pytest.fixture
    def milvus_client(self, mock_embeddings, milvus_config):
        """MilvusClient实例fixture."""
        return MilvusClient(embedding_function=mock_embeddings, config=milvus_config)

    @pytest.fixture
    def sample_vector_document(self):
        """示例向量文档fixture."""
        return VectorDocument(
            id="test_doc_1",
            fid="fault_001",
            content="这是一个测试故障描述",
            vector=[0.1, 0.2, 0.3] * 256,  # 768维向量
            metadata={"source": "test", "severity": "high"},
            document_type="fault",
            chunk_index=0,
            total_chunks=1,
            vector_dimension=768,
            embedding_model="test-model",
        )

    @pytest.mark.asyncio
    @patch("src.dayu_fault_rag.database.milvus_client.Milvus")
    async def test_connect_success(self, mock_milvus_class, milvus_client):
        """测试成功连接."""
        mock_vector_store = Mock()
        mock_milvus_class.return_value = mock_vector_store

        result = await milvus_client.connect()

        assert result is True
        assert milvus_client._connected is True
        assert milvus_client._vector_store == mock_vector_store
        mock_milvus_class.assert_called_once()

    @pytest.mark.asyncio
    @patch("src.dayu_fault_rag.database.milvus_client.Milvus")
    async def test_connect_failure(self, mock_milvus_class, milvus_client):
        """测试连接失败."""
        mock_milvus_class.side_effect = Exception("连接失败")

        result = await milvus_client.connect()

        assert result is False
        assert milvus_client._connected is False

    @pytest.mark.asyncio
    async def test_disconnect(self, milvus_client):
        """测试断开连接."""
        milvus_client._connected = True
        milvus_client._vector_store = Mock()
        milvus_client._retriever = Mock()

        await milvus_client.disconnect()

        assert milvus_client._connected is False
        assert milvus_client._vector_store is None
        assert milvus_client._retriever is None

    def test_is_connected(self, milvus_client):
        """测试连接状态检查."""
        # 初始状态
        assert milvus_client.is_connected() is False

        # 设置为已连接
        milvus_client._connected = True
        assert milvus_client.is_connected() is True

    @pytest.mark.asyncio
    async def test_add_documents_success(self, milvus_client, sample_vector_document):
        """测试成功添加文档."""
        # 设置mock向量存储
        mock_vector_store = AsyncMock()
        mock_vector_store.aadd_documents.return_value = ["doc_id_1"]
        milvus_client._vector_store = mock_vector_store
        milvus_client._connected = True

        result = await milvus_client.add_documents([sample_vector_document])

        assert result == ["doc_id_1"]
        mock_vector_store.aadd_documents.assert_called_once()

    @pytest.mark.asyncio
    async def test_add_documents_not_connected(
        self, milvus_client, sample_vector_document
    ):
        """测试未连接时添加文档."""
        with pytest.raises(RuntimeError, match="Milvus客户端未连接"):
            await milvus_client.add_documents([sample_vector_document])

    @pytest.mark.asyncio
    async def test_search_similar_success(self, milvus_client):
        """测试成功的相似性搜索."""
        # 设置mock向量存储
        mock_vector_store = AsyncMock()
        mock_doc = Document(
            page_content="测试内容",
            metadata={
                "id": "doc_1",
                "fid": "fault_001",
                "document_type": "fault",
                "chunk_index": 0,
                "total_chunks": 1,
                "vector_dimension": 768,
                "embedding_model": "test-model",
            },
        )
        mock_vector_store.asimilarity_search_with_score.return_value = [
            (mock_doc, 0.95)
        ]
        milvus_client._vector_store = mock_vector_store
        milvus_client._connected = True

        results = await milvus_client.search_similar("测试查询", k=5)

        assert len(results) == 1
        assert isinstance(results[0], VectorSearchResult)
        assert results[0].document.content == "测试内容"
        assert results[0].similarity_score == 0.95
        mock_vector_store.asimilarity_search_with_score.assert_called_once_with(
            query="测试查询", k=5, filter=None
        )

    @pytest.mark.asyncio
    async def test_search_by_vector_success(self, milvus_client):
        """测试成功的向量搜索."""
        # 设置mock向量存储
        mock_vector_store = AsyncMock()
        mock_doc = Document(
            page_content="测试内容",
            metadata={
                "id": "doc_1",
                "fid": "fault_001",
                "document_type": "fault",
                "chunk_index": 0,
                "total_chunks": 1,
                "vector_dimension": 768,
                "embedding_model": "test-model",
            },
        )
        mock_vector_store.asimilarity_search_by_vector_with_score.return_value = [
            (mock_doc, 0.92)
        ]
        milvus_client._vector_store = mock_vector_store
        milvus_client._connected = True

        test_vector = [0.1, 0.2, 0.3] * 256
        results = await milvus_client.search_by_vector(test_vector, k=3)

        assert len(results) == 1
        assert isinstance(results[0], VectorSearchResult)
        assert results[0].similarity_score == 0.92
        mock_vector_store.asimilarity_search_by_vector_with_score.assert_called_once_with(
            embedding=test_vector, k=3, filter=None
        )

    @pytest.mark.asyncio
    async def test_delete_documents_success(self, milvus_client):
        """测试成功删除文档."""
        # 设置mock向量存储
        mock_vector_store = AsyncMock()
        mock_vector_store.adelete.return_value = True
        milvus_client._vector_store = mock_vector_store
        milvus_client._connected = True

        result = await milvus_client.delete_documents(["doc_1", "doc_2"])

        assert result is True
        mock_vector_store.adelete.assert_called_once_with(ids=["doc_1", "doc_2"])

    @pytest.mark.asyncio
    async def test_get_collection_stats(self, milvus_client, milvus_config):
        """测试获取集合统计信息."""
        milvus_client._connected = True

        stats = await milvus_client.get_collection_stats()

        assert stats["collection_name"] == milvus_config.collection_name
        assert stats["connected"] is True
        assert stats["embedding_dimension"] == milvus_config.dimension
        assert stats["metric_type"] == milvus_config.metric_type

    def test_get_retriever(self, milvus_client):
        """测试获取检索器."""
        # 设置mock向量存储
        mock_vector_store = Mock()
        mock_retriever = Mock()
        mock_vector_store.as_retriever.return_value = mock_retriever
        milvus_client._vector_store = mock_vector_store
        milvus_client._connected = True

        retriever = milvus_client.get_retriever(
            search_type="mmr", search_kwargs={"k": 5}
        )

        assert retriever == mock_retriever
        mock_vector_store.as_retriever.assert_called_once_with(
            search_type="mmr", search_kwargs={"k": 5}
        )

    def test_retriever_property(self, milvus_client):
        """测试retriever属性."""
        # 设置mock向量存储
        mock_vector_store = Mock()
        mock_retriever = Mock()
        mock_vector_store.as_retriever.return_value = mock_retriever
        milvus_client._vector_store = mock_vector_store
        milvus_client._connected = True

        # 第一次访问
        retriever1 = milvus_client.retriever
        assert retriever1 == mock_retriever

        # 第二次访问应该返回缓存的检索器
        retriever2 = milvus_client.retriever
        assert retriever2 == mock_retriever
        assert retriever1 is retriever2

    @pytest.mark.asyncio
    async def test_from_documents_success(
        self, mock_embeddings, sample_vector_document
    ):
        """测试从文档创建客户端."""
        with patch.object(
            MilvusClient, "connect", new_callable=AsyncMock
        ) as mock_connect:
            with patch.object(
                MilvusClient, "add_documents", new_callable=AsyncMock
            ) as mock_add:
                mock_connect.return_value = True
                mock_add.return_value = ["doc_1"]

                client = await MilvusClient.from_documents(
                    documents=[sample_vector_document],
                    embedding_function=mock_embeddings,
                )

                assert isinstance(client, MilvusClient)
                mock_connect.assert_called_once()
                mock_add.assert_called_once_with([sample_vector_document])

    def test_vector_store_property_not_connected(self, milvus_client):
        """测试未连接时访问vector_store属性."""
        with pytest.raises(RuntimeError, match="Milvus客户端未连接"):
            _ = milvus_client.vector_store

    @pytest.mark.asyncio
    async def test_search_with_filter(self, milvus_client):
        """测试带过滤条件的搜索."""
        # 设置mock向量存储
        mock_vector_store = AsyncMock()
        mock_doc = Document(
            page_content="高优先级故障",
            metadata={
                "id": "doc_1",
                "fid": "fault_001",
                "document_type": "fault",
                "severity": "high",
            },
        )
        mock_vector_store.asimilarity_search_with_score.return_value = [
            (mock_doc, 0.88)
        ]
        milvus_client._vector_store = mock_vector_store
        milvus_client._connected = True

        filter_condition = {"severity": "high"}
        results = await milvus_client.search_similar(
            "查询内容", k=10, filter=filter_condition
        )

        assert len(results) == 1
        mock_vector_store.asimilarity_search_with_score.assert_called_once_with(
            query="查询内容", k=10, filter=filter_condition
        )
