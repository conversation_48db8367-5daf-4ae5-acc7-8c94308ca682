"""MySQL ORM客户端单元测试.

测试使用SQLAlchemy ORM的MySQL客户端实现。
"""

from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from sqlalchemy.exc import SQLAlchemyError

from src.dayu_fault_rag.config.settings import DatabaseConfig
from src.dayu_fault_rag.database.mysql_client import MySQLClient
from src.dayu_fault_rag.database.tables import FaultInfoORM
from src.dayu_fault_rag.models.fault import FaultInfo


@pytest.fixture
def db_config():
    """数据库配置fixture，包含过滤条件."""
    return DatabaseConfig(
        host="test-host",
        port=3306,
        username="test-user",
        password="test-password",
        database="test-db",
        charset="utf8mb4",
        pool_size=5,
        max_overflow=10,
        table_name="fault_info",
        report_type_filter="故障",
        stage_filter="已发布",
    )


@pytest.fixture
def mysql_client(db_config):
    """MySQL客户端fixture."""
    return MySQLClient(db_config)


@pytest.fixture
def sample_fault_orm():
    """示例故障ORM对象."""
    return FaultInfoORM(
        id=1,
        fid="F123456789",
        reportType="故障",
        stage="已发布",
        level="紧急",
        faultDescribe="服务响应超时",
        causeDescribe="网络延迟导致",
        problem="<p>详细分析内容</p>",
        playback='{"steps": ["step1", "step2"]}',
        improveAction='{"actions": ["action1"]}',
        lastUpdateTime=datetime(2024, 1, 2, 15, 30, 0),
        creator="test-user",
        recorder="test-reviewer",
    )


@pytest.fixture
def sample_fault_info():
    """示例故障信息数据模型."""
    return FaultInfo(
        id=1,
        fid="F123456789",
        reportType="故障",
        stage="已发布",
        level="紧急",
        status="已关闭",
        faultTitle="系统服务异常",
        faultDescribe="服务响应超时",
        faultAnalyze="网络延迟导致",
        problemAnalyze="<p>详细分析内容</p>",
        playback='{"steps": ["step1", "step2"]}',
        improve_action='{"actions": ["action1"]}',
        createdTime=datetime(2024, 1, 1, 10, 0, 0),
        lastUpdateTime=datetime(2024, 1, 2, 15, 30, 0),
        submitter="test-user",
        reviewer="test-reviewer",
    )


def setup_mock_session(result_value=None, scalar_value=None, scalars_value=None):
    """设置异步Session Mock的辅助函数."""
    mock_session = AsyncMock()
    mock_result = MagicMock()  # 使用MagicMock而不是AsyncMock

    # 设置不同类型的返回值
    if result_value is not None:
        mock_result.scalar_one_or_none.return_value = result_value
    else:
        # 明确设置为None
        mock_result.scalar_one_or_none.return_value = None

    if scalar_value is not None:
        mock_result.scalar.return_value = scalar_value
    else:
        # 明确设置为None
        mock_result.scalar.return_value = None

    if scalars_value is not None:
        # 创建mock scalars对象
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = scalars_value
        mock_result.scalars.return_value = mock_scalars

    # session.execute返回mock_result
    mock_session.execute.return_value = mock_result

    # 设置异步上下文管理器
    mock_session.__aenter__ = AsyncMock(return_value=mock_session)
    mock_session.__aexit__ = AsyncMock(return_value=None)

    return mock_session, mock_result


class TestMySQLClient:
    """MySQL ORM客户端测试类."""

    @pytest.mark.asyncio
    async def test_initialize_success(self, mysql_client):
        """测试成功初始化数据库引擎."""
        with (
            patch(
                "src.dayu_fault_rag.database.mysql_client.create_async_engine"
            ) as mock_create_engine,
            patch(
                "src.dayu_fault_rag.database.mysql_client.async_sessionmaker"
            ) as mock_sessionmaker,
        ):
            mock_engine = AsyncMock()
            mock_create_engine.return_value = mock_engine
            mock_session_factory = MagicMock()
            mock_sessionmaker.return_value = mock_session_factory

            await mysql_client.initialize()

            # 验证引擎创建参数 - 检查URL编码和连接参数
            expected_url = (
                "mysql+aiomysql://test-user:test-password"
                "@test-host:3306/test-db"
                "?charset=utf8mb4&autocommit=true"
            )
            mock_create_engine.assert_called_once_with(
                expected_url,
                pool_size=5,
                max_overflow=10,
                pool_pre_ping=True,
                echo=False,
            )

            # 验证session工厂创建
            mock_sessionmaker.assert_called_once()

            assert mysql_client._engine == mock_engine
            assert mysql_client._session_factory == mock_session_factory

    @pytest.mark.asyncio
    async def test_initialize_failure(self, mysql_client):
        """测试数据库引擎初始化失败."""
        with patch(
            "src.dayu_fault_rag.database.mysql_client.create_async_engine"
        ) as mock_create_engine:
            mock_create_engine.side_effect = Exception("连接失败")

            with pytest.raises(Exception, match="连接失败"):
                await mysql_client.initialize()

    @pytest.mark.asyncio
    async def test_close_engine(self, mysql_client):
        """测试关闭数据库引擎."""
        mock_engine = AsyncMock()
        mysql_client._engine = mock_engine

        await mysql_client.close()

        mock_engine.dispose.assert_called_once()

    def test_get_session_not_initialized(self, mysql_client):
        """测试未初始化时获取session抛出异常."""
        with pytest.raises(RuntimeError, match="数据库客户端未初始化"):
            mysql_client._get_session()

    def test_get_filter_info(self, mysql_client):
        """测试获取过滤配置信息."""
        filter_info = mysql_client.get_filter_info()

        expected = {
            "table_name": "fault_info",
            "report_type_filter": "故障",
            "stage_filter": "已发布",
        }
        assert filter_info == expected

    def test_build_base_query(self, mysql_client):
        """测试基础查询构建包含过滤条件."""
        query = mysql_client._build_base_query()

        # 验证查询构建成功
        assert query is not None
        # 具体的过滤条件在集成测试中验证

    def test_orm_to_fault_info_conversion(self, mysql_client, sample_fault_orm):
        """测试ORM对象到FaultInfo模型的转换."""
        fault_info = mysql_client._orm_to_fault_info(sample_fault_orm)

        assert fault_info.fid == "F123456789"
        assert fault_info.fault_describe == "服务响应超时"
        assert fault_info.cause_describe == "网络延迟导致"
        assert fault_info.level == "紧急"
        assert fault_info.problem == "<p>详细分析内容</p>"
        assert fault_info.playback == '{"steps": ["step1", "step2"]}'
        assert fault_info.improve_action == '{"actions": ["action1"]}'
        assert fault_info.last_update_time == datetime(2024, 1, 2, 15, 30, 0)

    @pytest.mark.asyncio
    async def test_get_fault_by_fid_found(self, mysql_client, sample_fault_orm):
        """测试根据FID获取故障信息 - 找到记录."""
        # Mock session和查询结果
        mock_session = AsyncMock()
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_fault_orm
        mock_session.execute.return_value = mock_result
        mock_session.__aenter__.return_value = mock_session
        mock_session.__aexit__.return_value = None

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        result = await mysql_client.get_fault_by_fid("F123456789")

        assert result is not None
        assert result.fid == "F123456789"
        assert result.fault_describe == "服务响应超时"
        assert result.cause_describe == "网络延迟导致"
        assert result.level == "紧急"
        assert result.problem == "<p>详细分析内容</p>"

    @pytest.mark.asyncio
    async def test_get_fault_by_fid_not_found(self, mysql_client):
        """测试根据FID未找到故障信息."""
        mock_session, mock_result = setup_mock_session(result_value=None)

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        result = await mysql_client.get_fault_by_fid("F999999999")

        assert result is None
        mock_session.execute.assert_called_once()
        mock_result.scalar_one_or_none.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_fault_by_fid_exception(self, mysql_client):
        """测试查询故障信息时发生SQLAlchemy异常."""
        mock_session = AsyncMock()
        mock_session.execute.side_effect = SQLAlchemyError("数据库错误")
        mock_session.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = AsyncMock(return_value=None)

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        with pytest.raises(SQLAlchemyError, match="数据库错误"):
            await mysql_client.get_fault_by_fid("F123456789")

    @pytest.mark.asyncio
    async def test_get_faults_by_filter(self, mysql_client, sample_fault_orm):
        """测试根据条件过滤获取故障信息列表."""
        # Mock session和查询结果
        mock_session = AsyncMock()
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_fault_orm]
        mock_session.execute.return_value = mock_result
        mock_session.__aenter__.return_value = mock_session
        mock_session.__aexit__.return_value = None

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        result = await mysql_client.get_faults_by_filter(
            level="紧急",
            start_time=datetime(2024, 1, 1),
            end_time=datetime(2024, 1, 3),
            limit=10,
            offset=0,
        )

        assert len(result) == 1
        assert result[0].fid == "F123456789"
        assert result[0].level == "紧急"
        assert result[0].fault_describe == "服务响应超时"

    @pytest.mark.asyncio
    async def test_get_faults_by_last_update_time(self, mysql_client, sample_fault_orm):
        """测试获取指定时间之后更新的故障信息."""
        # Mock session和查询结果
        mock_session = AsyncMock()
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [sample_fault_orm]
        mock_session.execute.return_value = mock_result
        mock_session.__aenter__.return_value = mock_session
        mock_session.__aexit__.return_value = None

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        result = await mysql_client.get_faults_by_last_update_time(
            datetime(2024, 1, 1), limit=1000
        )

        assert len(result) == 1
        assert result[0].fid == "F123456789"
        assert result[0].level == "紧急"
        assert result[0].fault_describe == "服务响应超时"

    @pytest.mark.asyncio
    async def test_get_total_fault_count(self, mysql_client):
        """测试获取故障总数."""
        mock_session, mock_result = setup_mock_session(scalar_value=1234)

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        count = await mysql_client.get_total_fault_count()

        assert count == 1234
        mock_session.execute.assert_called_once()
        mock_result.scalar.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_total_fault_count_no_data(self, mysql_client):
        """测试获取故障总数但没有数据."""
        mock_session, mock_result = setup_mock_session(scalar_value=None)

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        count = await mysql_client.get_total_fault_count()

        assert count == 0

    @pytest.mark.asyncio
    async def test_get_latest_update_time(self, mysql_client):
        """测试获取最新更新时间."""
        latest_time = datetime(2024, 1, 15, 12, 0, 0)
        mock_session, mock_result = setup_mock_session(scalar_value=latest_time)

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        result = await mysql_client.get_latest_update_time()

        assert result == latest_time
        mock_session.execute.assert_called_once()
        mock_result.scalar.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_latest_update_time_no_data(self, mysql_client):
        """测试获取最新更新时间但没有数据."""
        mock_session, mock_result = setup_mock_session(scalar_value=None)

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        result = await mysql_client.get_latest_update_time()

        assert result is None

    @pytest.mark.asyncio
    async def test_health_check_success(self, mysql_client):
        """测试数据库健康检查成功."""
        mock_session, mock_result = setup_mock_session()

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        result = await mysql_client.health_check()

        assert result is True
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_health_check_failure(self, mysql_client):
        """测试数据库健康检查失败."""
        mock_session = AsyncMock()
        mock_session.execute.side_effect = Exception("连接失败")
        mock_session.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = AsyncMock(return_value=None)

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        result = await mysql_client.health_check()

        assert result is False

    @pytest.mark.asyncio
    async def test_session_not_initialized_error(self, mysql_client):
        """测试session工厂未初始化时的错误处理."""
        # 不设置session工厂
        with pytest.raises(RuntimeError, match="数据库客户端未初始化"):
            await mysql_client.get_fault_by_fid("F123456789")

    @pytest.mark.asyncio
    async def test_sqlalchemy_error_handling(self, mysql_client):
        """测试SQLAlchemy异常的处理."""
        mock_session = AsyncMock()
        mock_session.execute.side_effect = SQLAlchemyError("ORM错误")
        mock_session.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = AsyncMock(return_value=None)

        mysql_client._session_factory = MagicMock(return_value=mock_session)

        # 测试各种方法都正确处理SQLAlchemy异常
        with pytest.raises(SQLAlchemyError):
            await mysql_client.get_fault_by_fid("F123456789")

        with pytest.raises(SQLAlchemyError):
            await mysql_client.get_faults_by_filter()

        with pytest.raises(SQLAlchemyError):
            await mysql_client.get_total_fault_count()

    def test_orm_type_safety(self, mysql_client, sample_fault_orm):
        """测试ORM类型安全性."""
        # 验证ORM对象的类型注解
        assert isinstance(sample_fault_orm.id, int)
        assert isinstance(sample_fault_orm.fid, str)
        assert isinstance(sample_fault_orm.level, str)
        assert isinstance(sample_fault_orm.faultDescribe, str)
        assert isinstance(sample_fault_orm.causeDescribe, str)
        assert isinstance(sample_fault_orm.problem, str)
        assert isinstance(sample_fault_orm.lastUpdateTime, datetime)
        assert isinstance(sample_fault_orm.playback, str)
        assert isinstance(sample_fault_orm.improveAction, str)

        # 验证转换后的数据模型类型
        fault_info = mysql_client._orm_to_fault_info(sample_fault_orm)
        assert isinstance(fault_info.fid, str)
        assert isinstance(fault_info.level, str)
        assert isinstance(fault_info.fault_describe, str)
        assert isinstance(fault_info.cause_describe, str)
        assert isinstance(fault_info.problem, str)

    @pytest.mark.asyncio
    async def test_initialize_with_special_characters(self):
        """测试用户名密码包含特殊字符时的URL编码."""
        # 创建包含特殊字符的配置
        special_config = DatabaseConfig(
            host="test-host",
            port=3306,
            username="test@user",  # 包含@符号
            password="test:pass/word",  # 包含:和/符号
            database="test-db",
            charset="utf8mb4",
            pool_size=5,
            max_overflow=10,
            table_name="fault_info",
            report_type_filter="故障",
            stage_filter="已发布",
        )

        client = MySQLClient(special_config)

        with (
            patch(
                "src.dayu_fault_rag.database.mysql_client.create_async_engine"
            ) as mock_create_engine,
            patch(
                "src.dayu_fault_rag.database.mysql_client.async_sessionmaker"
            ) as mock_sessionmaker,
        ):
            mock_engine = AsyncMock()
            mock_create_engine.return_value = mock_engine
            mock_session_factory = MagicMock()
            mock_sessionmaker.return_value = mock_session_factory

            await client.initialize()

            # 验证特殊字符被正确编码
            expected_url = (
                "mysql+aiomysql://test%40user:test%3Apass%2Fword"  # URL编码后的用户名密码
                "@test-host:3306/test-db"
                "?charset=utf8mb4&autocommit=true"
            )
            mock_create_engine.assert_called_once_with(
                expected_url,
                pool_size=5,
                max_overflow=10,
                pool_pre_ping=True,
                echo=False,
            )
