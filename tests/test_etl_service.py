"""ETL服务单元测试.

测试ETL数据处理服务的各个功能模块。
"""

from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from src.dayu_fault_rag.config.settings import ETLConfig
from src.dayu_fault_rag.models.fault import FaultInfo
from src.dayu_fault_rag.models.vector_document import DocumentChunk, VectorDocument
from src.dayu_fault_rag.services.etl_service import (
    ETLService,
    ETLStats,
    ETLWatermark,
    create_etl_service,
)


class TestETLWatermark:
    """测试ETL水印功能."""

    def test_watermark_to_dict(self):
        """测试水印转字典."""
        now = datetime.now()
        watermark = ETLWatermark(
            last_update_time=now,
            last_processed_fid="FAULT_001",
            total_processed=100,
            last_run_time=now,
        )

        result = watermark.to_dict()

        assert result["last_update_time"] == now.isoformat()
        assert result["last_processed_fid"] == "FAULT_001"
        assert result["total_processed"] == 100
        assert result["last_run_time"] == now.isoformat()

    def test_watermark_from_dict(self):
        """测试从字典创建水印."""
        now = datetime.now()
        data = {
            "last_update_time": now.isoformat(),
            "last_processed_fid": "FAULT_001",
            "total_processed": 100,
            "last_run_time": now.isoformat(),
        }

        watermark = ETLWatermark.from_dict(data)

        assert watermark.last_update_time.replace(microsecond=0) == now.replace(
            microsecond=0
        )
        assert watermark.last_processed_fid == "FAULT_001"
        assert watermark.total_processed == 100

    def test_watermark_from_dict_with_invalid_time(self):
        """测试处理无效时间格式."""
        data = {
            "last_update_time": "invalid-time",
            "last_processed_fid": "FAULT_001",
            "total_processed": 100,
        }

        watermark = ETLWatermark.from_dict(data)

        assert watermark.last_update_time is None
        assert watermark.last_processed_fid == "FAULT_001"
        assert watermark.total_processed == 100


class TestETLStats:
    """测试ETL统计功能."""

    def test_stats_duration_calculation(self):
        """测试统计耗时计算."""
        start_time = datetime.now()
        end_time = start_time + timedelta(seconds=30)

        stats = ETLStats(start_time=start_time, end_time=end_time)

        assert abs(stats.get_duration() - 30.0) < 0.1

    def test_stats_to_dict(self):
        """测试统计信息转字典."""
        start_time = datetime.now()
        end_time = start_time + timedelta(seconds=30)

        stats = ETLStats(
            extracted_count=100,
            transformed_count=95,
            loaded_count=90,
            error_count=5,
            start_time=start_time,
            end_time=end_time,
        )

        result = stats.to_dict()

        assert result["extracted_count"] == 100
        assert result["transformed_count"] == 95
        assert result["loaded_count"] == 90
        assert result["error_count"] == 5
        assert abs(result["duration_seconds"] - 30.0) < 0.1


@pytest.fixture
def mock_mysql_client():
    """Mock MySQL客户端."""
    client = AsyncMock()
    client.health_check.return_value = True
    client.get_total_fault_count.return_value = 1000
    client.get_latest_update_time.return_value = datetime.now()
    return client


@pytest.fixture
def mock_milvus_client():
    """Mock Milvus客户端."""
    client = AsyncMock()
    client.is_connected.return_value = True
    client.add_documents.return_value = ["doc1", "doc2", "doc3"]
    client.get_collection_stats.return_value = {
        "collection_name": "test_collection",
        "connected": True,
    }
    return client


@pytest.fixture
def mock_embedding_service():
    """Mock 向量化服务."""
    service = MagicMock()
    service.health_check.return_value = True
    service.get_cache_stats.return_value = {
        "memory_cache_size": 10,
        "disk_cache_size": 100,
    }
    service.config.model_name = "test-model"
    service.embed_documents.return_value = [
        [0.1] * 128,  # 128维测试向量
        [0.2] * 128,
        [0.3] * 128,
    ]
    return service


@pytest.fixture
def etl_config():
    """ETL配置."""
    return ETLConfig(
        batch_size=10,
        chunk_size=500,
        chunk_overlap=50,
        watermark_file="test_watermark.json",
    )


@pytest.fixture
def etl_service(
    mock_mysql_client, mock_milvus_client, mock_embedding_service, etl_config
):
    """ETL服务实例."""
    return ETLService(
        mysql_client=mock_mysql_client,
        milvus_client=mock_milvus_client,
        embedding_service=mock_embedding_service,
        config=etl_config,
    )


@pytest.fixture
def sample_fault_info():
    """示例故障信息."""
    return FaultInfo(
        fid="FAULT_TEST_001",
        fault_describe="测试故障描述",
        cause_describe="测试原因描述",
        level="P2",
        last_update_time=datetime.now(),
        title="测试故障标题",
        system_name="测试系统",
    )


class TestETLService:
    """测试ETL服务."""

    @pytest.mark.asyncio
    async def test_health_check(self, etl_service):
        """测试健康检查."""
        result = await etl_service.health_check()
        assert result is True

    @pytest.mark.asyncio
    async def test_health_check_failure(self, etl_service):
        """测试健康检查失败."""
        etl_service.mysql_client.health_check.return_value = False

        result = await etl_service.health_check()
        assert result is False

    @pytest.mark.asyncio
    async def test_extract_all_faults(self, etl_service, sample_fault_info):
        """测试全量数据抽取."""
        # Mock返回数据
        etl_service.mysql_client.get_faults_by_filter.side_effect = [
            [sample_fault_info],  # 第一批
            [],  # 第二批为空，结束
        ]

        faults = await etl_service.extract_all_faults()

        assert len(faults) == 1
        assert faults[0].fid == "FAULT_TEST_001"

    @pytest.mark.asyncio
    async def test_extract_incremental_faults(self, etl_service, sample_fault_info):
        """测试增量数据抽取."""
        last_update_time = datetime.now() - timedelta(hours=1)

        # Mock返回数据
        etl_service.mysql_client.get_faults_by_last_update_time.side_effect = [
            [sample_fault_info],  # 第一批
            [],  # 第二批为空，结束
        ]

        faults = await etl_service.extract_incremental_faults(last_update_time)

        assert len(faults) == 1
        assert faults[0].fid == "FAULT_TEST_001"

    @pytest.mark.asyncio
    async def test_extract_incremental_faults_no_watermark(
        self, etl_service, sample_fault_info
    ):
        """测试无水印时的增量抽取（应该执行全量）."""
        # Mock全量抽取
        etl_service.mysql_client.get_faults_by_filter.side_effect = [
            [sample_fault_info],
            [],
        ]

        faults = await etl_service.extract_incremental_faults(None)

        assert len(faults) == 1
        assert faults[0].fid == "FAULT_TEST_001"

    @pytest.mark.asyncio
    async def test_transform_fault_to_documents(self, etl_service, sample_fault_info):
        """测试故障转换为文档."""
        documents = await etl_service.transform_fault_to_documents(sample_fault_info)

        assert len(documents) > 0
        assert all(isinstance(doc, VectorDocument) for doc in documents)
        assert all(doc.fid == "FAULT_TEST_001" for doc in documents)
        assert all(len(doc.vector) == 128 for doc in documents)  # 测试向量维度

    @pytest.mark.asyncio
    async def test_transform_fault_empty_content(self, etl_service):
        """测试空内容故障转换."""
        fault = FaultInfo(fid="FAULT_EMPTY", fault_describe="", cause_describe="")

        documents = await etl_service.transform_fault_to_documents(fault)

        # 即使故障描述为空，也会生成一个包含故障ID的文档
        assert len(documents) >= 0  # 可能为0或1，都是合理的

    @pytest.mark.asyncio
    async def test_load_documents_to_milvus(self, etl_service):
        """测试文档加载到Milvus."""
        documents = [
            VectorDocument(
                fid="FAULT_001",
                content="测试内容1",
                vector=[0.1] * 128,
            ),
            VectorDocument(
                fid="FAULT_002",
                content="测试内容2",
                vector=[0.2] * 128,
            ),
        ]

        result = await etl_service.load_documents_to_milvus(documents)

        assert result is True
        etl_service.milvus_client.add_documents.assert_called_once_with(documents)

    @pytest.mark.asyncio
    async def test_load_documents_empty_list(self, etl_service):
        """测试空文档列表加载."""
        result = await etl_service.load_documents_to_milvus([])

        assert result is True
        etl_service.milvus_client.add_documents.assert_not_called()

    @pytest.mark.asyncio
    async def test_split_text_into_chunks(self, etl_service):
        """测试文本分块."""
        text = "这是一个很长的文本内容。" * 100  # 创建较长的文本

        chunks = await etl_service._split_text_into_chunks(text)

        assert len(chunks) > 0
        assert all(isinstance(chunk, DocumentChunk) for chunk in chunks)
        assert all(chunk.chunk_index < chunk.total_chunks for chunk in chunks)

    @pytest.mark.asyncio
    async def test_split_text_empty(self, etl_service):
        """测试空文本分块."""
        chunks = await etl_service._split_text_into_chunks("")

        assert len(chunks) == 0

    @pytest.mark.asyncio
    async def test_watermark_operations(self, etl_service, tmp_path):
        """测试水印操作."""
        # 设置临时水印文件路径
        watermark_file = tmp_path / "test_watermark.json"
        etl_service.watermark_file = watermark_file

        # 测试加载不存在的水印文件
        watermark = await etl_service._load_watermark()
        assert isinstance(watermark, ETLWatermark)
        assert watermark.total_processed == 0

        # 测试更新水印
        now = datetime.now()
        await etl_service._update_watermark(
            last_update_time=now,
            last_processed_fid="FAULT_001",
            total_processed=100,
        )

        # 测试加载已保存的水印
        loaded_watermark = await etl_service._load_watermark()
        assert loaded_watermark.last_processed_fid == "FAULT_001"
        assert loaded_watermark.total_processed == 100
        assert loaded_watermark.last_update_time.replace(microsecond=0) == now.replace(
            microsecond=0
        )

    @pytest.mark.asyncio
    async def test_run_full_etl(self, etl_service, sample_fault_info, tmp_path):
        """测试全量ETL流程."""
        # 设置临时水印文件
        etl_service.watermark_file = tmp_path / "test_watermark.json"

        # Mock数据抽取
        etl_service.mysql_client.get_faults_by_filter.side_effect = [
            [sample_fault_info],
            [],
        ]

        stats = await etl_service.run_full_etl()

        assert stats.extracted_count == 1
        assert stats.transformed_count >= 0
        assert stats.start_time is not None
        assert stats.end_time is not None

    @pytest.mark.asyncio
    async def test_run_incremental_etl(self, etl_service, sample_fault_info, tmp_path):
        """测试增量ETL流程."""
        # 设置临时水印文件
        etl_service.watermark_file = tmp_path / "test_watermark.json"

        # 创建一个带有水印的JSON文件
        import json
        from datetime import datetime, timedelta

        watermark_data = {
            "last_update_time": (datetime.now() - timedelta(hours=1)).isoformat(),
            "last_processed_fid": "FAULT_000",
            "total_processed": 50,
            "last_run_time": (datetime.now() - timedelta(hours=1)).isoformat(),
        }

        with open(etl_service.watermark_file, "w", encoding="utf-8") as f:
            json.dump(watermark_data, f)

        # Mock数据抽取
        etl_service.mysql_client.get_faults_by_last_update_time.side_effect = [
            [sample_fault_info],
            [],
        ]

        stats = await etl_service.run_incremental_etl()

        assert stats.extracted_count == 1
        assert stats.start_time is not None
        assert stats.end_time is not None

    @pytest.mark.asyncio
    async def test_get_etl_status(self, etl_service):
        """测试获取ETL状态."""
        status = await etl_service.get_etl_status()

        assert "watermark" in status
        assert "mysql_stats" in status
        assert "milvus_stats" in status
        assert "embedding_stats" in status
        assert "config" in status

    @pytest.mark.asyncio
    async def test_process_faults_batch_with_errors(
        self, etl_service, sample_fault_info
    ):
        """测试批量处理带错误的故障数据."""
        # Mock向量化失败
        etl_service.embedding_service.embed_documents.side_effect = Exception(
            "向量化失败"
        )

        stats = ETLStats()
        faults = [sample_fault_info]

        await etl_service._process_faults_batch(faults, stats)

        # 向量化失败会导致skipped_count增加（而不是error_count）
        assert stats.skipped_count > 0 or stats.error_count > 0

    @pytest.mark.asyncio
    async def test_process_faults_batch_with_load_failure(
        self, etl_service, sample_fault_info
    ):
        """测试批量处理加载失败的情况."""
        # Mock加载失败
        etl_service.milvus_client.add_documents.side_effect = Exception("加载失败")

        stats = ETLStats()
        faults = [sample_fault_info]

        await etl_service._process_faults_batch(faults, stats)

        # 应该有错误计数
        assert stats.error_count > 0 or stats.transformed_count > 0


@pytest.mark.asyncio
async def test_create_etl_service():
    """测试创建ETL服务函数."""
    with (
        patch("src.dayu_fault_rag.services.etl_service.MySQLClient") as mock_mysql,
        patch("src.dayu_fault_rag.services.etl_service.MilvusClient") as mock_milvus,
        patch(
            "src.dayu_fault_rag.services.embedding_service.get_embedding_service"
        ) as mock_embedding,
    ):
        # Mock实例
        mock_mysql_instance = AsyncMock()
        mock_mysql.return_value = mock_mysql_instance

        mock_milvus_instance = AsyncMock()
        mock_milvus.return_value = mock_milvus_instance

        mock_embedding_instance = MagicMock()
        mock_embedding.return_value = mock_embedding_instance

        # 创建ETL服务
        etl_service = await create_etl_service()

        assert isinstance(etl_service, ETLService)
        mock_mysql_instance.initialize.assert_called_once()
        mock_milvus_instance.connect.assert_called_once()


class TestETLIntegration:
    """ETL集成测试（需要真实数据库连接时跳过）."""

    @pytest.mark.skip(reason="需要真实数据库连接")
    @pytest.mark.asyncio
    async def test_real_etl_pipeline(self):
        """测试真实ETL流程（集成测试）."""
        # 这个测试需要真实的数据库连接
        # 在CI/CD环境中可能需要跳过
        etl_service = await create_etl_service()

        # 健康检查
        is_healthy = await etl_service.health_check()
        if not is_healthy:
            pytest.skip("ETL服务不健康，跳过集成测试")

        # 运行少量增量ETL测试
        stats = await etl_service.run_incremental_etl()
        assert stats.start_time is not None
        assert stats.end_time is not None

        # 清理
        await etl_service.mysql_client.close()
        await etl_service.milvus_client.disconnect()
