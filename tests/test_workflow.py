"""基于LangGraph的故障相似性检索工作流测试."""

from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from dayu_fault_rag.agent.workflow import (
    FaultSearchState,
    FaultSearchWorkflow,
    LangGraphSearchConfig,
    create_fault_search_workflow,
)
from dayu_fault_rag.database.milvus_client import MilvusClient
from dayu_fault_rag.database.mysql_client import MySQLClient
from dayu_fault_rag.models.fault import FaultInfo, FaultSearchResult
from dayu_fault_rag.services.embedding_service import EmbeddingService


@pytest.fixture
def mock_mysql_client():
    """创建Mock MySQL客户端."""
    client = AsyncMock(spec=MySQLClient)
    return client


@pytest.fixture
def mock_milvus_client():
    """创建Mock Milvus客户端."""
    client = AsyncMock(spec=MilvusClient)
    return client


@pytest.fixture
def mock_embedding_service():
    """创建Mock向量化服务."""
    service = MagicMock(spec=EmbeddingService)
    service.embed_query = MagicMock(return_value=[[0.1, 0.2, 0.3, 0.4]])
    service.health_check = MagicMock(return_value=True)
    return service


@pytest.fixture
def mock_reranker():
    """创建Mock reranker."""

    class MockReranker:
        def compress_documents(self, documents, query):
            # 按原始顺序返回，但添加分数
            for i, doc in enumerate(documents):
                doc.metadata["relevance_score"] = 0.9 - (i * 0.1)
            return documents

    return MockReranker()


@pytest.fixture
def workflow_config():
    """创建工作流配置."""
    return LangGraphSearchConfig(
        top_k=5,
        similarity_threshold=0.6,
        use_rerank=True,
        rerank_top_n=3,
    )


@pytest.fixture
def fault_search_workflow(
    mock_mysql_client, mock_milvus_client, mock_embedding_service, workflow_config
):
    """创建FaultSearchWorkflow实例."""
    with patch("dayu_fault_rag.agent.workflow.Qwen3Rerank") as mock_rerank_class:
        mock_rerank_instance = mock_reranker()
        mock_rerank_class.return_value = mock_rerank_instance

        workflow = FaultSearchWorkflow(
            mysql_client=mock_mysql_client,
            milvus_client=mock_milvus_client,
            embedding_service=mock_embedding_service,
            config=workflow_config,
        )
        return workflow


@pytest.fixture
def sample_fault():
    """创建示例故障信息."""
    return FaultInfo(
        fid="FAULT_001",
        title="数据库连接超时",
        faultDescribe="用户反馈系统响应缓慢，数据库连接超时",
        causeDescribe="连接池配置不当，最大连接数设置过低",
        level="P2",
        system_name="订单系统",
        service_name="支付服务",
        last_update_time=datetime.now(),
    )


@pytest.fixture
def sample_similar_docs():
    """创建示例相似文档结果."""
    return [
        {
            "content": "数据库连接池配置问题导致系统响应缓慢",
            "score": 0.85,
            "metadata": {"fid": "FAULT_002"},
        },
        {
            "content": "数据库连接超时问题分析与解决",
            "score": 0.78,
            "metadata": {"fid": "FAULT_003"},
        },
        {
            "content": "系统性能优化：连接池调优实践",
            "score": 0.72,
            "metadata": {"fid": "FAULT_004"},
        },
    ]


class TestLangGraphSearchConfig:
    """测试LangGraph搜索配置."""

    def test_default_config(self):
        """测试默认配置."""
        config = LangGraphSearchConfig()
        assert config.top_k == 10
        assert config.similarity_threshold == 0.5
        assert config.use_rerank is True
        assert config.rerank_top_n == 5
        assert config.rerank_threshold == 0.3

    def test_custom_config(self):
        """测试自定义配置."""
        config = LangGraphSearchConfig(
            top_k=3,
            similarity_threshold=0.7,
            use_rerank=False,
            rerank_top_n=2,
            rerank_threshold=0.5,
        )
        assert config.top_k == 3
        assert config.similarity_threshold == 0.7
        assert config.use_rerank is False
        assert config.rerank_top_n == 2
        assert config.rerank_threshold == 0.5


class TestFaultSearchWorkflow:
    """测试FaultSearchWorkflow类."""

    @pytest.mark.asyncio
    async def test_workflow_initialization(self, fault_search_workflow):
        """测试工作流初始化."""
        assert fault_search_workflow.mysql_client is not None
        assert fault_search_workflow.milvus_client is not None
        assert fault_search_workflow.embedding_service is not None
        assert fault_search_workflow.config.top_k == 5
        assert fault_search_workflow.reranker is not None

    @pytest.mark.asyncio
    async def test_search_similar_faults_success(
        self, fault_search_workflow, sample_fault, sample_similar_docs
    ):
        """测试成功执行相似故障搜索."""
        # 设置Mock返回值
        fault_search_workflow.mysql_client.get_fault_by_id.return_value = sample_fault
        fault_search_workflow.milvus_client.similarity_search.return_value = (
            sample_similar_docs
        )

        # 创建相似故障的完整信息
        similar_faults = [
            FaultInfo(
                fid="FAULT_002",
                title="数据库连接池问题",
                faultDescribe="数据库连接池配置问题",
                level="P2",
                system_name="订单系统",
            ),
            FaultInfo(
                fid="FAULT_003",
                title="数据库连接超时分析",
                faultDescribe="数据库连接超时问题分析",
                level="P2",
                system_name="订单系统",
            ),
            FaultInfo(
                fid="FAULT_004",
                title="系统性能优化",
                faultDescribe="系统性能优化实践",
                level="P2",
                system_name="订单系统",
            ),
        ]

        fault_search_workflow.mysql_client.get_fault_by_id.side_effect = [
            sample_fault,  # 目标故障
            *similar_faults,  # 相似故障
        ]

        # 执行测试
        result = await fault_search_workflow.search_similar_faults("FAULT_001")

        # 验证结果结构
        assert "results" in result
        assert "total_count" in result
        assert "execution_time" in result
        assert "used_rerank" in result
        assert "error" in result

        # 验证返回结果
        assert len(result["results"]) == 3
        assert result["total_count"] == 3
        assert result["used_rerank"] is True
        assert result["error"] is None
        assert all(isinstance(r, FaultSearchResult) for r in result["results"])

    @pytest.mark.asyncio
    async def test_search_similar_faults_without_rerank(
        self, fault_search_workflow, sample_fault, sample_similar_docs
    ):
        """测试不使用rerank的搜索."""
        fault_search_workflow.mysql_client.get_fault_by_id.return_value = sample_fault
        fault_search_workflow.milvus_client.similarity_search.return_value = (
            sample_similar_docs
        )

        similar_faults = [
            FaultInfo(fid="FAULT_002", title="故障2", level="P2"),
            FaultInfo(fid="FAULT_003", title="故障3", level="P2"),
            FaultInfo(fid="FAULT_004", title="故障4", level="P2"),
        ]

        fault_search_workflow.mysql_client.get_fault_by_id.side_effect = [
            sample_fault,
            *similar_faults,
        ]

        result = await fault_search_workflow.search_similar_faults(
            "FAULT_001", use_rerank=False
        )

        assert result["used_rerank"] is False
        assert len(result["results"]) == 3

    @pytest.mark.asyncio
    async def test_search_fault_not_found(self, fault_search_workflow):
        """测试故障ID不存在的情况."""
        fault_search_workflow.mysql_client.get_fault_by_id.return_value = None

        result = await fault_search_workflow.search_similar_faults("NON_EXISTENT")

        assert result["results"] == []
        assert result["total_count"] == 0
        assert result["error"] is not None
        assert "不存在" in result["error"]

    @pytest.mark.asyncio
    async def test_search_empty_text_content(self, fault_search_workflow):
        """测试故障没有有效文本内容的情况."""
        empty_fault = FaultInfo(
            fid="FAULT_EMPTY",
            title="",
            faultDescribe="",
            causeDescribe="",
            level="P2",
        )
        fault_search_workflow.mysql_client.get_fault_by_id.return_value = empty_fault

        result = await fault_search_workflow.search_similar_faults("FAULT_EMPTY")

        assert result["results"] == []
        assert result["total_count"] == 0
        assert result["error"] is not None
        assert "文本内容" in result["error"]

    @pytest.mark.asyncio
    async def test_search_no_similar_results(self, fault_search_workflow, sample_fault):
        """测试没有找到相似故障的情况."""
        fault_search_workflow.mysql_client.get_fault_by_id.return_value = sample_fault
        fault_search_workflow.milvus_client.similarity_search.return_value = []

        result = await fault_search_workflow.search_similar_faults("FAULT_001")

        assert result["results"] == []
        assert result["total_count"] == 0
        assert result["error"] is None

    @pytest.mark.asyncio
    async def test_search_with_custom_limit(
        self, fault_search_workflow, sample_fault, sample_similar_docs
    ):
        """测试自定义结果数量限制."""
        fault_search_workflow.mysql_client.get_fault_by_id.return_value = sample_fault
        fault_search_workflow.milvus_client.similarity_search.return_value = (
            sample_similar_docs
        )

        similar_faults = [
            FaultInfo(fid="FAULT_002", title="故障2", level="P2"),
            FaultInfo(fid="FAULT_003", title="故障3", level="P2"),
            FaultInfo(fid="FAULT_004", title="故障4", level="P2"),
        ]

        fault_search_workflow.mysql_client.get_fault_by_id.side_effect = [
            sample_fault,
            *similar_faults,
        ]

        result = await fault_search_workflow.search_similar_faults("FAULT_001", limit=2)

        assert len(result["results"]) == 2
        assert result["total_count"] == 2

    @pytest.mark.asyncio
    async def test_search_with_min_score(
        self, fault_search_workflow, sample_fault, sample_similar_docs
    ):
        """测试自定义相似度阈值."""
        fault_search_workflow.mysql_client.get_fault_by_id.return_value = sample_fault
        fault_search_workflow.milvus_client.similarity_search.return_value = (
            sample_similar_docs
        )

        similar_faults = [
            FaultInfo(fid="FAULT_002", title="故障2", level="P2"),
            FaultInfo(fid="FAULT_003", title="故障3", level="P2"),
            FaultInfo(fid="FAULT_004", title="故障4", level="P2"),
        ]

        fault_search_workflow.mysql_client.get_fault_by_id.side_effect = [
            sample_fault,
            *similar_faults,
        ]

        result = await fault_search_workflow.search_similar_faults(
            "FAULT_001", min_score=0.8
        )

        # 只有第一个文档的分数>=0.8
        assert len(result["results"]) <= 1

    @pytest.mark.asyncio
    async def test_rerank_failure_handling(
        self, fault_search_workflow, sample_fault, sample_similar_docs
    ):
        """测试rerank失败时的处理."""
        fault_search_workflow.mysql_client.get_fault_by_id.return_value = sample_fault
        fault_search_workflow.milvus_client.similarity_search.return_value = (
            sample_similar_docs
        )

        # 模拟rerank失败
        fault_search_workflow.reranker.compress_documents.side_effect = Exception(
            "Rerank failed"
        )

        similar_faults = [
            FaultInfo(fid="FAULT_002", title="故障2", level="P2"),
            FaultInfo(fid="FAULT_003", title="故障3", level="P2"),
            FaultInfo(fid="FAULT_004", title="故障4", level="P2"),
        ]

        fault_search_workflow.mysql_client.get_fault_by_id.side_effect = [
            sample_fault,
            *similar_faults,
        ]

        result = await fault_search_workflow.search_similar_faults("FAULT_001")

        # 应该使用原始结果而不是失败
        assert len(result["results"]) == 3
        assert result["error"] is None

    @pytest.mark.asyncio
    async def test_health_check_success(self, fault_search_workflow):
        """测试健康检查成功."""
        fault_search_workflow.mysql_client.health_check.return_value = True
        fault_search_workflow.milvus_client.is_connected.return_value = True
        fault_search_workflow.embedding_service.health_check.return_value = True

        result = await fault_search_workflow.health_check()

        assert result is True

    @pytest.mark.asyncio
    async def test_health_check_failure(self, fault_search_workflow):
        """测试健康检查失败."""
        fault_search_workflow.mysql_client.health_check.return_value = False
        fault_search_workflow.milvus_client.is_connected.return_value = True
        fault_search_workflow.embedding_service.health_check.return_value = True

        result = await fault_search_workflow.health_check()

        assert result is False


class TestCreateFaultSearchWorkflow:
    """测试创建工作流实例的便利函数."""

    @pytest.mark.asyncio
    async def test_create_workflow_with_custom_clients(
        self,
        mock_mysql_client,
        mock_milvus_client,
        mock_embedding_service,
        workflow_config,
    ):
        """测试使用自定义客户端创建工作流."""
        workflow = await create_fault_search_workflow(
            mysql_client=mock_mysql_client,
            milvus_client=mock_milvus_client,
            embedding_service=mock_embedding_service,
            config=workflow_config,
        )

        assert isinstance(workflow, FaultSearchWorkflow)
        assert workflow.config.top_k == 5

    @pytest.mark.asyncio
    async def test_create_workflow_with_defaults(self):
        """测试使用默认配置创建工作流."""
        with (
            patch("dayu_fault_rag.agent.workflow.MySQLClient") as mock_mysql_class,
            patch("dayu_fault_rag.agent.workflow.MilvusClient") as mock_milvus_class,
            patch(
                "dayu_fault_rag.agent.workflow.get_embedding_service"
            ) as mock_embed_class,
            patch("dayu_fault_rag.agent.workflow.get_settings") as mock_settings,
        ):
            # 设置mock返回值
            mock_mysql_instance = AsyncMock()
            mock_mysql_instance.initialize = AsyncMock()
            mock_mysql_class.return_value = mock_mysql_instance

            mock_milvus_instance = AsyncMock()
            mock_milvus_instance.connect = AsyncMock()
            mock_milvus_class.return_value = mock_milvus_instance

            mock_embed_instance = MagicMock()
            mock_embed_class.return_value = mock_embed_instance

            mock_settings.return_value = MagicMock()

            workflow = await create_fault_search_workflow()

            assert isinstance(workflow, FaultSearchWorkflow)
            mock_mysql_instance.initialize.assert_called_once()
            mock_milvus_instance.connect.assert_called_once()


class TestFaultSearchState:
    """测试FaultSearchState数据模型."""

    def test_state_structure(self):
        """测试状态结构."""
        state = FaultSearchState(
            fault_id="FAULT_001",
            limit=5,
            min_score=0.6,
            use_rerank=True,
            target_fault=None,
            target_text="测试文本",
            query_vector=[0.1, 0.2, 0.3],
            raw_results=[],
            reranked_results=[],
            final_results=[],
            execution_time=0.0,
            error=None,
        )

        assert state["fault_id"] == "FAULT_001"
        assert state["limit"] == 5
        assert state["min_score"] == 0.6
        assert state["use_rerank"] is True
        assert state["target_text"] == "测试文本"
        assert state["query_vector"] == [0.1, 0.2, 0.3]


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
