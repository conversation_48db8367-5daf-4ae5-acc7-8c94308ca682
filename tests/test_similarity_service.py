"""相似故障检索服务的单元测试."""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import List, Optional

from dayu_fault_rag.services.similarity_service import (
    SimilarityService,
    SimilaritySearchConfig,
    create_similarity_service,
)
from dayu_fault_rag.models.fault import FaultInfo, FaultSearchResult
from dayu_fault_rag.database.mysql_client import MySQLClient
from dayu_fault_rag.database.milvus_client import MilvusClient
from dayu_fault_rag.services.embedding_service import EmbeddingService


@pytest.fixture
def mock_mysql_client():
    """创建Mock MySQL客户端."""
    client = AsyncMock(spec=MySQLClient)
    return client


@pytest.fixture
def mock_milvus_client():
    """创建Mock Milvus客户端."""
    client = AsyncMock(spec=MilvusClient)
    return client


@pytest.fixture
def mock_embedding_service():
    """创建Mock向量化服务."""
    service = MagicMock(spec=EmbeddingService)
    service.embed_query = MagicMock(return_value=[[0.1, 0.2, 0.3]])
    service.health_check = MagicMock(return_value=True)
    return service


@pytest.fixture
def similarity_service(mock_mysql_client, mock_milvus_client, mock_embedding_service):
    """创建SimilarityService实例."""
    config = SimilaritySearchConfig(top_k=5, similarity_threshold=0.6)
    return SimilarityService(
        mysql_client=mock_mysql_client,
        milvus_client=mock_milvus_client,
        embedding_service=mock_embedding_service,
        config=config,
    )


@pytest.fixture
def sample_fault():
    """创建示例故障信息."""
    return FaultInfo(
        fid="FAULT_001",
        title="数据库连接超时",
        fault_describe="用户反馈系统响应缓慢，数据库连接超时",
        cause_describe="连接池配置不当，最大连接数设置过低",
        level="P2",
        system_name="订单系统",
        service_name="支付服务",
        last_update_time=datetime.now(),
    )


@pytest.fixture
def sample_similar_docs():
    """创建示例相似文档结果."""
    return [
        {
            "content": "数据库连接池配置问题导致系统响应缓慢",
            "score": 0.85,
            "metadata": {"fid": "FAULT_002"},
        },
        {
            "content": "数据库连接超时问题分析与解决",
            "score": 0.78,
            "metadata": {"fid": "FAULT_003"},
        },
        {
            "content": "系统性能优化：连接池调优实践",
            "score": 0.72,
            "metadata": {"fid": "FAULT_004"},
        },
    ]


class TestSimilarityService:
    """测试SimilarityService类."""

    @pytest.mark.asyncio
    async def test_find_similar_faults_success(self, similarity_service, sample_fault, sample_similar_docs):
        """测试成功查找相似故障."""
        # 设置Mock返回值
        similarity_service.mysql_client.get_fault_by_id.return_value = sample_fault
        similarity_service.milvus_client.similarity_search.return_value = sample_similar_docs
        
        # 创建相似故障的完整信息
        similar_fault = FaultInfo(
            fid="FAULT_002",
            title="数据库连接池问题",
            fault_describe="数据库连接池配置问题",
            level="P2",
            system_name="订单系统",
        )
        similarity_service.mysql_client.get_fault_by_id.side_effect = [
            sample_fault,  # 目标故障
            similar_fault,  # 相似故障1
            None,  # 相似故障2不存在
            None,  # 相似故障3不存在
        ]
        
        # 执行测试
        results = await similarity_service.find_similar_faults("FAULT_001")
        
        # 验证结果
        assert len(results) == 1  # 只有FAULT_002有完整信息
        assert results[0].fault_info.fid == "FAULT_002"
        assert results[0].similarity_score == 0.85
        
        # 验证调用
        similarity_service.mysql_client.get_fault_by_id.assert_called()
        similarity_service.milvus_client.similarity_search.assert_called_once()

    @pytest.mark.asyncio
    async def test_find_similar_faults_fault_not_found(self, similarity_service):
        """测试故障ID不存在的情况."""
        similarity_service.mysql_client.get_fault_by_id.return_value = None
        
        with pytest.raises(ValueError, match="故障ID 'NON_EXISTENT' 不存在"):
            await similarity_service.find_similar_faults("NON_EXISTENT")

    @pytest.mark.asyncio
    async def test_find_similar_faults_empty_text(self, similarity_service, sample_fault):
        """测试目标故障没有有效文本内容的情况."""
        # 创建一个没有有效文本内容的故障
        empty_fault = FaultInfo(
            fid="FAULT_EMPTY",
            title="",
            fault_describe="",
            cause_describe="",
        )
        similarity_service.mysql_client.get_fault_by_id.return_value = empty_fault
        
        results = await similarity_service.find_similar_faults("FAULT_EMPTY")
        
        assert results == []

    @pytest.mark.asyncio
    async def test_find_similar_faults_no_similar(self, similarity_service, sample_fault):
        """测试没有找到相似故障的情况."""
        similarity_service.mysql_client.get_fault_by_id.return_value = sample_fault
        similarity_service.milvus_client.similarity_search.return_value = []
        
        results = await similarity_service.find_similar_faults("FAULT_001")
        
        assert results == []

    @pytest.mark.asyncio
    async def test_find_similar_faults_with_custom_limit(self, similarity_service, sample_fault, sample_similar_docs):
        """测试自定义结果数量限制."""
        similarity_service.mysql_client.get_fault_by_id.return_value = sample_fault
        similarity_service.milvus_client.similarity_search.return_value = sample_similar_docs
        
        # 创建足够数量的相似故障
        mock_faults = []
        for i, doc in enumerate(sample_similar_docs):
            fault = FaultInfo(
                fid=doc["metadata"]["fid"],
                title=f"相似故障{i+1}",
                fault_describe=f"描述{i+1}",
                level="P2",
            )
            mock_faults.append(fault)
        
        similarity_service.mysql_client.get_fault_by_id.side_effect = [
            sample_fault,  # 目标故障
            *mock_faults,  # 相似故障
        ]
        
        results = await similarity_service.find_similar_faults("FAULT_001", limit=2)
        
        assert len(results) == 2
        assert results[0].similarity_score >= results[1].similarity_score

    @pytest.mark.asyncio
    async def test_find_similar_faults_with_min_score(self, similarity_service, sample_fault, sample_similar_docs):
        """测试自定义相似度阈值."""
        similarity_service.mysql_client.get_fault_by_id.return_value = sample_fault
        similarity_service.milvus_client.similarity_search.return_value = sample_similar_docs
        
        # 创建低于阈值的相似故障
        mock_faults = []
        for doc in sample_similar_docs:
            if doc["score"] >= 0.8:  # 阈值
                fault = FaultInfo(
                    fid=doc["metadata"]["fid"],
                    title="相似故障",
                    fault_describe="描述",
                    level="P2",
                )
                mock_faults.append(fault)
        
        similarity_service.mysql_client.get_fault_by_id.side_effect = [
            sample_fault,  # 目标故障
            *mock_faults,
        ]
        
        results = await similarity_service.find_similar_faults("FAULT_001", min_score=0.8)
        
        assert all(result.similarity_score >= 0.8 for result in results)

    @pytest.mark.asyncio
    async def test_cache_functionality(self, similarity_service, sample_fault, sample_similar_docs):
        """测试缓存功能."""
        similarity_service.mysql_client.get_fault_by_id.return_value = sample_fault
        similarity_service.milvus_client.similarity_search.return_value = sample_similar_docs
        
        # 第一次调用
        results1 = await similarity_service.find_similar_faults("FAULT_001", use_cache=True)
        
        # 第二次调用，应该使用缓存
        results2 = await similarity_service.find_similar_faults("FAULT_001", use_cache=True)
        
        # 验证结果相同
        assert len(results1) == len(results2)
        
        # 验证MySQL只被调用一次（因为缓存）
        expected_calls = 2  # 目标故障 + 相似故障
        # 由于mock设置，实际调用次数可能不同

    @pytest.mark.asyncio
    async def test_health_check_success(self, similarity_service):
        """测试健康检查成功."""
        similarity_service.mysql_client.health_check.return_value = True
        similarity_service.milvus_client.is_connected.return_value = True
        similarity_service.embedding_service.health_check.return_value = True
        
        result = await similarity_service.health_check()
        
        assert result is True

    @pytest.mark.asyncio
    async def test_health_check_failure(self, similarity_service):
        """测试健康检查失败."""
        similarity_service.mysql_client.health_check.return_value = False
        similarity_service.milvus_client.is_connected.return_value = True
        similarity_service.embedding_service.health_check.return_value = True
        
        result = await similarity_service.health_check()
        
        assert result is False

    @pytest.mark.asyncio
    async def test_clear_cache(self, similarity_service):
        """测试清除缓存."""
        # 设置一些缓存
        similarity_service._cache["test_key"] = ["test_result"]
        similarity_service._cache_timestamps["test_key"] = **********.0
        
        await similarity_service.clear_cache()
        
        assert len(similarity_service._cache) == 0
        assert len(similarity_service._cache_timestamps) == 0

    @pytest.mark.asyncio
    async def test_get_collection_stats(self, similarity_service):
        """测试获取集合统计信息."""
        expected_stats = {"total_docs": 100, "collection_name": "test"}
        similarity_service.milvus_client.get_collection_stats.return_value = expected_stats
        
        stats = await similarity_service.get_collection_stats()
        
        assert stats == expected_stats

    @pytest.mark.asyncio
    async def test_create_similarity_service_function(self):
        """测试创建相似性服务的便利函数."""
        with patch('dayu_fault_rag.services.similarity_service.get_settings') as mock_settings:
            mock_settings.return_value = MagicMock()
            
            with patch('dayu_fault_rag.services.similarity_service.MySQLClient') as mock_mysql:
                with patch('dayu_fault_rag.services.similarity_service.MilvusClient') as mock_milvus:
                    with patch('dayu_fault_rag.services.similarity_service.get_embedding_service') as mock_embed:
                        # 设置mock返回值
                        mock_mysql_instance = AsyncMock()
                        mock_mysql_instance.initialize = AsyncMock()
                        mock_mysql.return_value = mock_mysql_instance
                        
                        mock_milvus_instance = AsyncMock()
                        mock_milvus_instance.connect = AsyncMock()
                        mock_milvus.return_value = mock_milvus_instance
                        
                        mock_embed_instance = MagicMock()
                        mock_embed.return_value = mock_embed_instance
                        
                        # 测试创建服务
                        service = await create_similarity_service()
                        
                        assert isinstance(service, SimilarityService)
                        mock_mysql_instance.initialize.assert_called_once()
                        mock_milvus_instance.connect.assert_called_once()

    def test_similarity_search_config(self):
        """测试相似性搜索配置."""
        config = SimilaritySearchConfig(top_k=10, similarity_threshold=0.75)
        
        assert config.top_k == 10
        assert config.similarity_threshold == 0.75
        assert config.include_metadata is True
        assert config.max_content_length == 500

    def test_extract_matched_content(self, similarity_service):
        """测试提取匹配内容摘要."""
        # 测试短文本
        short_text = "这是一个短文本"
        result = similarity_service._extract_matched_content(short_text, 100)
        assert result == short_text
        
        # 测试长文本
        long_text = "这是一个很长的文本这是一个很长的文本这是一个很长的文本这是一个很长的文本"
        result = similarity_service._extract_matched_content(long_text, 10)
        assert len(result) <= 13  # 10 + "..."
        assert result.endswith("...")

    @pytest.mark.asyncio
    async def test_runtime_error_handling(self, similarity_service, sample_fault):
        """测试运行时错误处理."""
        similarity_service.mysql_client.get_fault_by_id.return_value = sample_fault
        similarity_service.milvus_client.similarity_search.side_effect = RuntimeError("Milvus error")
        
        with pytest.raises(RuntimeError, match="相似故障检索失败"):
            await similarity_service.find_similar_faults("FAULT_001")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])