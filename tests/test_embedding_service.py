"""Embedding服务单元测试."""

import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

import pytest
import requests

from src.dayu_fault_rag.config.settings import EmbeddingConfig
from src.dayu_fault_rag.services.embedding_service import (
    EmbeddingCache,
    EmbeddingService,
    Qwen3Embedding,
)


class TestEmbeddingCache:
    """向量化缓存测试类."""

    def test_cache_key_generation(self):
        """测试缓存键生成."""
        cache = EmbeddingCache()
        model_name = "Qwen/Qwen3-Embedding-4B"

        # 测试相同文本生成相同键
        key1 = cache._get_cache_key("test text", model_name)
        key2 = cache._get_cache_key("test text", model_name)
        assert key1 == key2

        # 测试不同文本生成不同键
        key3 = cache._get_cache_key("different text", model_name)
        assert key1 != key3

    def test_memory_cache(self):
        """测试内存缓存."""
        cache = EmbeddingCache(max_memory_size=10)  # 增大缓存大小以避免LRU问题
        model_name = "Qwen/Qwen3-Embedding-4B"

        # 添加缓存项
        vector1 = [0.1, 0.2, 0.3]
        vector2 = [0.4, 0.5, 0.6]

        cache.put("text1", vector1, model_name)
        cache.put("text2", vector2, model_name)

        # 验证缓存命中
        assert cache.get("text1", model_name) == vector1
        assert cache.get("text2", model_name) == vector2

        # 验证缓存未命中
        assert cache.get("nonexistent", model_name) is None

    def test_disk_cache(self):
        """测试磁盘缓存."""
        with tempfile.TemporaryDirectory() as temp_dir:
            cache_dir = Path(temp_dir)
            cache = EmbeddingCache(cache_dir=cache_dir, enable_disk_cache=True)
            model_name = "Qwen/Qwen3-Embedding-4B"

            vector = [0.1, 0.2, 0.3]
            cache.put("test_text", vector, model_name)

            # 创建新的缓存实例来验证持久化
            new_cache = EmbeddingCache(cache_dir=cache_dir, enable_disk_cache=True)
            assert new_cache.get("test_text", model_name) == vector

    def test_disk_cache_disabled(self):
        """测试禁用磁盘缓存."""
        cache = EmbeddingCache(enable_disk_cache=False)
        model_name = "Qwen/Qwen3-Embedding-4B"

        vector = [0.1, 0.2, 0.3]
        cache.put("test_text", vector, model_name)

        # 验证内存缓存仍然工作
        assert cache.get("test_text", model_name) == vector


class TestQwen3Embedding:
    """Qwen3Embedding测试类."""

    @pytest.fixture
    def embedding_config(self):
        """嵌入配置fixture."""
        return EmbeddingConfig(
            base_url="http://test.com",
            model_name="Qwen/Qwen3-Embedding-4B",
            timeout=30,
            max_length=512,
            batch_size=32,
            dimension=2560,
        )

    @pytest.fixture
    def qwen3_embedding(self, embedding_config):
        """Qwen3Embedding实例fixture."""
        return Qwen3Embedding(config=embedding_config)

    @patch("requests.post")
    def test_embed_documents_success(self, mock_post, qwen3_embedding):
        """测试成功嵌入文档."""
        # 模拟API响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": [
                {"embedding": [0.1] * 2560, "index": 0},
                {"embedding": [0.2] * 2560, "index": 1},
            ]
        }
        mock_post.return_value = mock_response

        texts = ["文本1", "文本2"]
        embeddings = qwen3_embedding.embed_documents(texts)

        assert len(embeddings) == 2
        assert len(embeddings[0]) == 2560
        assert len(embeddings[1]) == 2560
        mock_post.assert_called_once()

    @patch("requests.post")
    def test_embed_query_success(self, mock_post, qwen3_embedding):
        """测试成功嵌入查询."""
        # 模拟API响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": [{"embedding": [0.1] * 2560, "index": 0}]
        }
        mock_post.return_value = mock_response

        text = "查询文本"
        embedding = qwen3_embedding.embed_query(text)

        assert len(embedding) == 2560
        mock_post.assert_called_once()

    @patch("requests.post")
    def test_embed_with_api_error(self, mock_post, qwen3_embedding):
        """测试API错误处理."""
        # 模拟API错误
        mock_post.side_effect = requests.RequestException("API错误")

        with pytest.raises(Exception, match="API错误"):
            qwen3_embedding.embed_query("测试文本")

    @patch("requests.post")
    def test_embed_with_http_error(self, mock_post, qwen3_embedding):
        """测试HTTP错误处理."""
        # 模拟HTTP错误
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "服务器错误"
        mock_post.return_value = mock_response

        with pytest.raises(Exception, match="API请求失败"):
            qwen3_embedding.embed_query("测试文本")

    def test_get_dimension(self, qwen3_embedding):
        """测试获取向量维度."""
        dimension = qwen3_embedding.get_dimension()
        assert dimension == 2560


class TestEmbeddingService:
    """EmbeddingService测试类."""

    @pytest.fixture
    def embedding_config(self):
        """嵌入配置fixture."""
        return EmbeddingConfig(
            base_url="http://test.com",
            model_name="Qwen/Qwen3-Embedding-4B",
            timeout=30,
            max_length=512,
            batch_size=2,  # 小批次用于测试
            dimension=2560,
        )

    @pytest.fixture
    def embedding_service(self, embedding_config):
        """EmbeddingService实例fixture."""
        return EmbeddingService(config=embedding_config)

    @pytest.mark.asyncio
    @patch("requests.post")
    async def test_embed_text_success(self, mock_post, embedding_service):
        """测试成功嵌入单个文本."""
        # 模拟API响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": [{"embedding": [0.1] * 2560, "index": 0}]
        }
        mock_post.return_value = mock_response

        text = "测试文本"
        embedding = await embedding_service.embed_text(text)

        assert len(embedding) == 2560
        assert embedding[0] == 0.1

    @pytest.mark.asyncio
    @patch("requests.post")
    async def test_embed_texts_batch_processing(self, mock_post, embedding_service):
        """测试批量文本处理."""

        # 模拟API响应
        def mock_response_side_effect(*args, **kwargs):
            response = Mock()
            response.status_code = 200
            # 根据请求的文本数量返回相应的嵌入
            request_data = json.loads(kwargs["data"])
            text_count = len(request_data["input"])
            response.json.return_value = {
                "data": [
                    {"embedding": [0.1 + i * 0.1] * 2560, "index": i}
                    for i in range(text_count)
                ]
            }
            return response

        mock_post.side_effect = mock_response_side_effect

        texts = ["文本1", "文本2", "文本3", "文本4", "文本5"]
        embeddings = await embedding_service.embed_texts(texts)

        assert len(embeddings) == 5
        assert len(embeddings[0]) == 2560

        # 验证批处理：5个文本，批次大小为2，应该有3个API调用
        assert mock_post.call_count == 3

    @pytest.mark.asyncio
    async def test_cache_functionality(self, embedding_service):
        """测试缓存功能."""
        with patch.object(
            embedding_service.embedding_model, "embed_query"
        ) as mock_embed:
            mock_embed.return_value = [0.1] * 2560

            text = "缓存测试文本"

            # 第一次调用
            embedding1 = await embedding_service.embed_text(text)
            assert mock_embed.call_count == 1

            # 第二次调用应该使用缓存
            embedding2 = await embedding_service.embed_text(text)
            assert mock_embed.call_count == 1  # 没有增加
            assert embedding1 == embedding2

    @pytest.mark.asyncio
    async def test_error_handling(self, embedding_service):
        """测试错误处理."""
        with patch.object(
            embedding_service.embedding_model, "embed_query"
        ) as mock_embed:
            mock_embed.side_effect = Exception("嵌入失败")

            with pytest.raises(Exception, match="嵌入失败"):
                await embedding_service.embed_text("测试文本")

    @pytest.mark.asyncio
    async def test_get_stats(self, embedding_service):
        """测试获取统计信息."""
        with patch.object(
            embedding_service.embedding_model, "embed_query"
        ) as mock_embed:
            mock_embed.return_value = [0.1] * 2560

            # 嵌入一些文本
            await embedding_service.embed_text("文本1")
            await embedding_service.embed_text("文本2")
            await embedding_service.embed_text("文本1")  # 缓存命中

            stats = embedding_service.get_stats()

            assert stats["total_requests"] == 3
            assert stats["cache_hits"] == 1
            assert stats["cache_misses"] == 2
            assert stats["cache_hit_rate"] == 1 / 3
            assert "cache_size" in stats
