# 大禹故障RAG系统 Makefile
# 简化常用开发和部署操作

.PHONY: help install dev test build docker-build docker-dev docker-prod docker-stop docker-clean lint format

# 默认目标
.DEFAULT_GOAL := help

# 颜色定义
BLUE := \033[36m
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m # No Color

# 项目信息
PROJECT_NAME := dayu-fault-rag
VERSION := 0.1.0
DOCKER_IMAGE := $(PROJECT_NAME):$(VERSION)

help: ## 显示帮助信息
	@echo "$(BLUE)大禹故障RAG系统 - 可用命令:$(NC)"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "$(GREEN)%-20s$(NC) %s\n", $$1, $$2}'

install: ## 安装项目依赖
	@echo "$(BLUE)安装项目依赖...$(NC)"
	uv sync
	@echo "$(GREEN)依赖安装完成$(NC)"

install-dev: ## 安装开发依赖
	@echo "$(BLUE)安装开发依赖...$(NC)"
	uv sync --dev
	@echo "$(GREEN)开发依赖安装完成$(NC)"

dev: ## 启动开发环境
	@echo "$(BLUE)启动开发环境...$(NC)"
	python main.py mcp

etl: ## 运行ETL服务
	@echo "$(BLUE)启动ETL服务...$(NC)"
	python main.py etl-service

etl-full: ## 执行全量ETL
	@echo "$(BLUE)执行全量ETL...$(NC)"
	python main.py etl full

etl-incremental: ## 执行增量ETL
	@echo "$(BLUE)执行增量ETL...$(NC)"
	python main.py etl incremental

test: ## 运行测试
	@echo "$(BLUE)运行测试...$(NC)"
	pytest

test-cov: ## 运行测试并生成覆盖率报告
	@echo "$(BLUE)运行测试并生成覆盖率报告...$(NC)"
	pytest --cov=dayu_fault_rag --cov-report=html --cov-report=term

lint: ## 代码检查
	@echo "$(BLUE)运行代码检查...$(NC)"
	ruff check src/ tests/

format: ## 代码格式化
	@echo "$(BLUE)格式化代码...$(NC)"
	ruff format src/ tests/

build: ## 构建Python包
	@echo "$(BLUE)构建Python包...$(NC)"
	uv build

clean: ## 清理构建文件
	@echo "$(BLUE)清理构建文件...$(NC)"
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	@echo "$(GREEN)清理完成$(NC)"

# Docker相关命令
docker-build: ## 构建Docker镜像
	@echo "$(BLUE)构建Docker镜像...$(NC)"
	docker build -t $(DOCKER_IMAGE) -t $(PROJECT_NAME):latest .
	@echo "$(GREEN)Docker镜像构建完成$(NC)"

docker-dev: ## 启动Docker开发环境
	@echo "$(BLUE)启动Docker开发环境...$(NC)"
	./scripts/docker-deploy.sh dev

docker-prod: ## 启动Docker生产环境
	@echo "$(BLUE)启动Docker生产环境...$(NC)"
	./scripts/docker-deploy.sh prod

docker-stop: ## 停止Docker服务
	@echo "$(BLUE)停止Docker服务...$(NC)"
	./scripts/docker-deploy.sh stop

docker-clean: ## 清理Docker环境
	@echo "$(YELLOW)清理Docker环境...$(NC)"
	./scripts/docker-deploy.sh clean

docker-logs: ## 查看Docker日志
	@echo "$(BLUE)查看Docker日志...$(NC)"
	./scripts/docker-deploy.sh logs

docker-status: ## 查看Docker服务状态
	@echo "$(BLUE)查看Docker服务状态...$(NC)"
	./scripts/docker-deploy.sh status

# 健康检查
health: ## 运行健康检查
	@echo "$(BLUE)运行健康检查...$(NC)"
	python scripts/healthcheck.py

# 数据库相关
db-setup: ## 初始化数据库
	@echo "$(BLUE)初始化数据库...$(NC)"
	python scripts/setup_database.py

db-migrate: ## 数据库迁移
	@echo "$(BLUE)执行数据库迁移...$(NC)"
	# TODO: 添加数据库迁移逻辑

# 配置相关
config-check: ## 检查配置
	@echo "$(BLUE)检查配置...$(NC)"
	python scripts/test_config.py

config-example: ## 复制配置示例
	@echo "$(BLUE)复制配置示例...$(NC)"
	cp env.example .env
	@echo "$(YELLOW)请编辑 .env 文件配置必要的环境变量$(NC)"

# 部署相关
deploy-dev: docker-build docker-dev ## 构建并部署开发环境

deploy-prod: docker-build docker-prod ## 构建并部署生产环境

# 监控相关
logs: ## 查看应用日志
	@echo "$(BLUE)查看应用日志...$(NC)"
	tail -f logs/*.log 2>/dev/null || echo "$(YELLOW)日志文件不存在$(NC)"

# 完整流程
setup: install config-example db-setup ## 完整项目设置
	@echo "$(GREEN)项目设置完成！$(NC)"
	@echo "$(YELLOW)请编辑 .env 文件配置环境变量，然后运行 'make dev' 启动开发环境$(NC)"

# 版本信息
version: ## 显示版本信息
	@echo "$(BLUE)项目版本: $(VERSION)$(NC)"
	@echo "$(BLUE)Python版本:$(NC) $(shell python --version)"
	@echo "$(BLUE)Docker版本:$(NC) $(shell docker --version 2>/dev/null || echo '未安装')"
