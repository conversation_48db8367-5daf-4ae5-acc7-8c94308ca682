# 大禹故障RAG系统环境变量配置示例
# 复制此文件为 .env 并修改相应的值

# ===========================================
# 基础环境配置
# ===========================================
DAYU_ENVIRONMENT=development
DAYU_PROJECT_NAME=Dayu Fault RAG System
DAYU_VERSION=0.1.0
DAYU_DEBUG=true

# ===========================================
# MySQL数据库配置
# ===========================================
DAYU_MYSQL_HOST=**************
DAYU_MYSQL_PORT=3306
DAYU_MYSQL_USERNAME=fmx_readonly
DAYU_MYSQL_PASSWORD=Fmx@R9!pL2e$1
DAYU_MYSQL_DATABASE=itmp_sys

# ===========================================
# Milvus向量数据库配置
# ===========================================
DAYU_MILVUS_HOST=https://in01-55c8dcbceba5eb8.tc-ap-beijing.vectordb.zilliz.com.cn
DAYU_MILVUS_PORT=443
DAYU_MILVUS_USERNAME=yw_ops
DAYU_MILVUS_PASSWORD=Jb3c6eLigbqOIm
DAYU_MILVUS_COLLECTION=dayu_fault_vectors

# ===========================================
# API服务配置
# ===========================================
DAYU_API_HOST=0.0.0.0
DAYU_API_PORT=8000
DAYU_API_DEBUG=false

# ===========================================
# 向量化服务配置
# ===========================================
DAYU_EMBEDDING_BASE_URL=http://ray.ttyuyin.com:10001/ray-qwen3-emb
DAYU_EMBEDDING_MODEL=Qwen/Qwen3-Embedding-4B
DAYU_EMBEDDING_TIMEOUT=30

# ===========================================
# 日志配置
# ===========================================
DAYU_LOG_LEVEL=INFO
DAYU_LOG_FILE=


# ===========================================
# LLM配置
# ===========================================
DAYU_LLM_PROVIDER=openai
DAYU_LLM_MODEL=Qwen/Qwen3-32B
DAYU_LLM_EXTERNAL_ARGS='{"base_url": “http://ray.ttyuyin.com:10001/vllm-qwen3-32b/v1”, "api_key": "sk-quwan-jarvis"}'
DAYU_LLM_EXTRA_BODY='{ “chat_template_kwargs” = { ”enable_thinking“ = ”false“ } }'




# ===========================================
# ETL配置（可选）
# ===========================================
# ETL相关配置通常使用代码默认值，如需自定义可设置以下变量
# DAYU_ETL_BATCH_SIZE=100
# DAYU_ETL_SYNC_INTERVAL=3600
# DAYU_ETL_CHUNK_SIZE=1000
# DAYU_ETL_CHUNK_OVERLAP=200
# DAYU_ETL_WATERMARK_FILE=etl_watermark.json

# ===========================================
# 监控和告警配置（可选）
# ===========================================
# DAYU_ALERT_WEBHOOK_URL=https://hooks.slack.com/your/webhook/url
# DAYU_METRICS_ENABLED=true
# DAYU_HEALTH_CHECK_INTERVAL=60

# ===========================================
# 生产环境示例配置
# ===========================================
# 取消注释以下配置用于生产环境

# # 基础配置
# DAYU_ENVIRONMENT=production
# DAYU_DEBUG=false

# # 数据库配置（生产环境）
# DAYU_MYSQL_HOST=prod-mysql.example.com
# DAYU_MYSQL_PORT=3306
# DAYU_MYSQL_USERNAME=app_user
# DAYU_MYSQL_PASSWORD=secure_password_here
# DAYU_MYSQL_DATABASE=itmp_prod

# # Milvus配置（生产环境）
# DAYU_MILVUS_HOST=prod-milvus.example.com
# DAYU_MILVUS_PORT=19530
# DAYU_MILVUS_USERNAME=milvus_user
# DAYU_MILVUS_PASSWORD=secure_milvus_password
# DAYU_MILVUS_COLLECTION=fault_vectors_prod

# # API配置（生产环境）
# DAYU_API_HOST=0.0.0.0
# DAYU_API_PORT=8000
# DAYU_API_DEBUG=false

# # 日志配置（生产环境）
# DAYU_LOG_LEVEL=INFO
# DAYU_LOG_FILE=/var/log/dayu-fault-rag/app.log 