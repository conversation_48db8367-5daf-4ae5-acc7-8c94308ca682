#!/usr/bin/env python3
"""大禹故障RAG系统主入口

支持通过命令行参数指定运行不同的服务：
- mcp: MCP服务（默认）
- etl: ETL服务
- etl-service: ETL后台服务
"""

import argparse
import asyncio
import sys

# 添加项目根目录到路径
from src.dayu_fault_rag.mcp.server import mcp
from src.dayu_fault_rag.utils.logging import get_logger, setup_logging


def run_mcp_service():
    """运行MCP服务."""
    logger = get_logger(__name__)
    logger.info("启动MCP服务...")
    mcp.run(transport="sse")


async def run_etl_service():
    """运行ETL后台服务."""
    logger = get_logger(__name__)
    logger.info("启动ETL后台服务. 定时同步数据到Milvus...")

    # 导入ETL服务运行器
    from scripts.run_etl_service import ETLServiceRunner

    runner = ETLServiceRunner()

    try:
        if await runner.initialize():
            await runner.run_continuous_sync()
        else:
            logger.error("ETL服务初始化失败")
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止ETL服务...")
        await runner.cleanup()
    except Exception as e:
        logger.error(f"ETL服务运行异常: {e}")
        sys.exit(1)


async def run_etl_once(etl_type: str):
    """运行单次ETL处理."""
    logger = get_logger(__name__)
    logger.info(f"执行{etl_type}ETL处理...")

    # 导入ETL服务
    from scripts.run_etl_service import ETLServiceRunner

    runner = ETLServiceRunner()

    try:
        if await runner.initialize():
            if etl_type == "full":
                await runner.run_single_sync("full")
            else:
                await runner.run_single_sync("incremental")
        else:
            logger.error("ETL服务初始化失败", exc_info=True)
            sys.exit(1)
    except Exception as e:
        logger.error(f"ETL处理失败: {e}")
        sys.exit(1)


def main():
    """主函数."""
    parser = argparse.ArgumentParser(
        description="大禹故障RAG系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main.py                    # 运行MCP服务（默认）
  python main.py mcp                # 运行MCP服务
  python main.py etl-service        # 运行ETL后台服务
  python main.py etl full           # 执行全量ETL
  python main.py etl incremental    # 执行增量ETL
        """,
    )

    parser.add_argument(
        "service",
        nargs="?",
        default="mcp",
        choices=["mcp", "etl-service", "etl"],
        help="要运行的服务",
    )

    parser.add_argument(
        "etl_type",
        nargs="?",
        choices=["full", "incremental"],
        default="incremental",
        help="ETL类型（仅在service为etl时使用）",
    )

    args = parser.parse_args()

    # 设置日志
    setup_logging()
    logger = get_logger(__name__)

    logger.info(f"启动服务: {args.service}")

    try:
        if args.service == "mcp":
            run_mcp_service()
        elif args.service == "etl-service":
            asyncio.run(run_etl_service())
        elif args.service == "etl":
            asyncio.run(run_etl_once(args.etl_type))
        else:
            logger.error(f"不支持的服务: {args.service}")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在退出...")
        sys.exit(0)
    except Exception as e:
        logger.error(f"服务运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
