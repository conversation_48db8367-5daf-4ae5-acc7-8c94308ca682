version: '3.8'

services:
  # ETL服务
  dayu-etl:
    build: .
    container_name: dayu-etl
    restart: unless-stopped
    environment:
      - DAYU_ENVIRONMENT=production
      - DAYU_MYSQL_HOST=${DAYU_MYSQL_HOST}
      - DAYU_MYSQL_PORT=${DAYU_MYSQL_PORT}
      - DAYU_MYSQL_USERNAME=${DAYU_MYSQL_USERNAME}
      - DAYU_MYSQL_PASSWORD=${DAYU_MYSQL_PASSWORD}
      - DAYU_MYSQL_DATABASE=${DAYU_MYSQL_DATABASE}
      - DAYU_MILVUS_HOST=${DAYU_MILVUS_HOST}
      - DAYU_MILVUS_PORT=${DAYU_MILVUS_PORT}
      - DAYU_MILVUS_COLLECTION=${DAYU_MILVUS_COLLECTION}
      - DAYU_LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    command: ["python", "main.py", "etl-service"]

  # MCP服务
  dayu-mcp:
    build: .
    container_name: dayu-mcp
    restart: unless-stopped
    ports:
      - "8001:8001"
    environment:
      - DAYU_ENVIRONMENT=production
      - DAYU_MYSQL_HOST=${DAYU_MYSQL_HOST}
      - DAYU_MYSQL_PORT=${DAYU_MYSQL_PORT}
      - DAYU_MYSQL_USERNAME=${DAYU_MYSQL_USERNAME}
      - DAYU_MYSQL_PASSWORD=${DAYU_MYSQL_PASSWORD}
      - DAYU_MYSQL_DATABASE=${DAYU_MYSQL_DATABASE}
      - DAYU_MILVUS_HOST=${DAYU_MILVUS_HOST}
      - DAYU_MILVUS_PORT=${DAYU_MILVUS_PORT}
      - DAYU_MILVUS_COLLECTION=${DAYU_MILVUS_COLLECTION}
      - DAYU_LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    command: ["python", "main.py", "mcp"]