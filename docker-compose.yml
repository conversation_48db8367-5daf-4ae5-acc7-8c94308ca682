# 大禹故障RAG系统 Docker Compose 配置
# 支持多服务部署：ETL服务、MCP服务
version: "3.8"

services:
  # ETL服务 - 数据同步和向量化处理
  dayu-etl:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: dayu-fault-rag:latest
    container_name: dayu-etl
    restart: unless-stopped
    environment:
      - DAYU_ENVIRONMENT=production
      - DAYU_MYSQL_HOST=${DAYU_MYSQL_HOST:-localhost}
      - DAYU_MYSQL_PORT=${DAYU_MYSQL_PORT:-3306}
      - DAYU_MYSQL_USERNAME=${DAYU_MYSQL_USERNAME:-root}
      - DAYU_MYSQL_PASSWORD=${DAYU_MYSQL_PASSWORD}
      - DAYU_MYSQL_DATABASE=${DAYU_MYSQL_DATABASE:-itmp}
      - DAYU_MILVUS_HOST=${DAYU_MILVUS_HOST:-localhost}
      - DAYU_MILVUS_PORT=${DAYU_MILVUS_PORT:-19530}
      - DAYU_MILVUS_COLLECTION=${DAYU_MILVUS_COLLECTION:-fault_vectors}
      - DAYU_EMBEDDING_BASE_URL=${DAYU_EMBEDDING_BASE_URL}
      - DAYU_EMBEDDING_MODEL=${DAYU_EMBEDDING_MODEL:-Qwen/Qwen3-Embedding-4B}
      - DAYU_LOG_LEVEL=${DAYU_LOG_LEVEL:-INFO}
    volumes:
      - dayu_data:/app/data
      - dayu_logs:/app/logs
      - ./etl_watermark.json:/app/etl_watermark.json
    command: ["python", "main.py", "etl-service"]
    networks:
      - dayu-network
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    depends_on:
      - mysql
      - milvus

  # MCP服务 - AI工具接口
  dayu-mcp:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    image: dayu-fault-rag:latest
    container_name: dayu-mcp
    restart: unless-stopped
    ports:
      - "${DAYU_API_PORT:-8001}:8001"
    environment:
      - DAYU_ENVIRONMENT=production
      - DAYU_MYSQL_HOST=${DAYU_MYSQL_HOST:-localhost}
      - DAYU_MYSQL_PORT=${DAYU_MYSQL_PORT:-3306}
      - DAYU_MYSQL_USERNAME=${DAYU_MYSQL_USERNAME:-root}
      - DAYU_MYSQL_PASSWORD=${DAYU_MYSQL_PASSWORD}
      - DAYU_MYSQL_DATABASE=${DAYU_MYSQL_DATABASE:-itmp}
      - DAYU_MILVUS_HOST=${DAYU_MILVUS_HOST:-localhost}
      - DAYU_MILVUS_PORT=${DAYU_MILVUS_PORT:-19530}
      - DAYU_MILVUS_COLLECTION=${DAYU_MILVUS_COLLECTION:-fault_vectors}
      - DAYU_EMBEDDING_BASE_URL=${DAYU_EMBEDDING_BASE_URL}
      - DAYU_EMBEDDING_MODEL=${DAYU_EMBEDDING_MODEL:-Qwen/Qwen3-Embedding-4B}
      - DAYU_LOG_LEVEL=${DAYU_LOG_LEVEL:-INFO}
    volumes:
      - dayu_data:/app/data
      - dayu_logs:/app/logs
    command: ["python", "main.py", "mcp"]
    networks:
      - dayu-network
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    depends_on:
      - mysql
      - milvus

  # MySQL数据库 (可选 - 用于开发测试)
  mysql:
    image: mysql:8.0
    container_name: dayu-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-dayu123456}
      - MYSQL_DATABASE=${DAYU_MYSQL_DATABASE:-itmp}
      - MYSQL_USER=${DAYU_MYSQL_USERNAME:-dayu}
      - MYSQL_PASSWORD=${DAYU_MYSQL_PASSWORD:-dayu123456}
    ports:
      - "${MYSQL_PORT:-3306}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - dayu-network
    command: --default-authentication-plugin=mysql_native_password
    profiles:
      - dev

  # Milvus向量数据库 (可选 - 用于开发测试)
  milvus:
    image: milvusdb/milvus:v2.3.0
    container_name: dayu-milvus
    restart: unless-stopped
    ports:
      - "${MILVUS_PORT:-19530}:19530"
    environment:
      - ETCD_ENDPOINTS=etcd:2379
      - MINIO_ADDRESS=minio:9000
    volumes:
      - milvus_data:/var/lib/milvus
    networks:
      - dayu-network
    depends_on:
      - etcd
      - minio
    profiles:
      - dev

  # Etcd (Milvus依赖)
  etcd:
    image: quay.io/coreos/etcd:v3.5.0
    container_name: dayu-etcd
    restart: unless-stopped
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=4294967296
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    networks:
      - dayu-network
    profiles:
      - dev

  # MinIO (Milvus依赖)
  minio:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    container_name: dayu-minio
    restart: unless-stopped
    environment:
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
    ports:
      - "${MINIO_PORT:-9001}:9001"
    volumes:
      - minio_data:/data
    command: minio server /data --console-address ":9001"
    networks:
      - dayu-network
    profiles:
      - dev

# 网络配置
networks:
  dayu-network:
    driver: bridge
    name: dayu-network

# 数据卷配置
volumes:
  dayu_data:
    name: dayu_data
  dayu_logs:
    name: dayu_logs
  mysql_data:
    name: dayu_mysql_data
  milvus_data:
    name: dayu_milvus_data
  etcd_data:
    name: dayu_etcd_data
  minio_data:
    name: dayu_minio_data
