需求

我要做一个RAG应用

目标是: 找到相似故障

对外: 可以直接提问也可以对外提供MCP工具, 也有一个简单的前端页面可以给我验证

我的输入是只能从一个mysql数据库, 数据库信息如下

```jsx
10.192.237.247 
port：3306
用户名：fmx_readonly
密码：Fmx@R9!pL2e$1

生产环境库：itmp_sys
测试环境库：itmp

表名：fault_info
权限：只读

生产环境库：itmp_sys
测试环境库：itmp
```

表结构如下

# 数据库表结构

```jsx

-- itmp_sys.fault_info definition

CREATE TABLE `fault_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fid` varchar(256) DEFAULT NULL,
  `faultDescribe` varchar(256) DEFAULT NULL,
  `meetingMinutes` varchar(256) DEFAULT NULL,
  `currentProgress` varchar(256) DEFAULT NULL,
  `faultYear` varchar(256) DEFAULT NULL,
  `faultMonth` varchar(256) DEFAULT NULL,
  `faultQuarter` varchar(256) DEFAULT NULL,
  `startTime` datetime(6) DEFAULT NULL,
  `discoveryTime` datetime(6) DEFAULT NULL,
  `discoveryConsuming` int(11) DEFAULT NULL,
  `responseTime` datetime(6) DEFAULT NULL,
  `responseConsuming` int(11) DEFAULT NULL,
  `loacteTime` datetime(6) DEFAULT NULL,
  `loacteConsuming` int(11) DEFAULT NULL,
  `recoverTime` datetime(6) DEFAULT NULL,
  `recoverConsuming` int(11) DEFAULT NULL,
  `duration` int(11) DEFAULT NULL,
  `playback` json DEFAULT NULL,
  `causeClassify` varchar(256) DEFAULT NULL,
  `isChange` varchar(256) DEFAULT NULL,
  `infrastructure` varchar(256) DEFAULT NULL,
  `otherReason` json DEFAULT NULL,
  `causeDescribe` longtext,
  `serviceClassification` varchar(256) DEFAULT NULL,
  `isEffectProduce` json DEFAULT NULL,
  `directEffectRevenue` varchar(256) DEFAULT NULL,
  `isGrade` varchar(256) DEFAULT NULL,
  `level` varchar(256) DEFAULT NULL,
  `effectContent` longtext,
  `isEffectOutside` varchar(256) DEFAULT NULL,
  `hasWatch` varchar(256) DEFAULT NULL,
  `hasAlert` varchar(256) DEFAULT NULL,
  `isFirstLaunch` varchar(256) DEFAULT NULL,
  `firstLaunchSource` json DEFAULT NULL,
  `feedbackSource` varchar(256) DEFAULT NULL,
  `causeLocator` varchar(256) DEFAULT NULL,
  `isDevLocate` varchar(256) DEFAULT NULL,
  `isOpsLocate` varchar(256) DEFAULT NULL,
  `causeLocateTool` json DEFAULT NULL,
  `improveAction` json DEFAULT NULL,
  `handler` json DEFAULT NULL,
  `driver` varchar(256) DEFAULT NULL,
  `recorder` varchar(256) DEFAULT NULL,
  `simpleReportTime` datetime(6) DEFAULT NULL,
  `simpleReportDelay` varchar(256) DEFAULT NULL,
  `preparationTime` datetime(6) DEFAULT NULL,
  `replayTime` datetime(6) DEFAULT NULL,
  `replayDelay` varchar(256) DEFAULT NULL,
  `finishFileTime` datetime(6) DEFAULT NULL,
  `finishFileDelay` varchar(256) DEFAULT NULL,
  `recoveryPlan` varchar(256) DEFAULT NULL,
  `creator` varchar(256) DEFAULT NULL,
  `creatorID` varchar(256) DEFAULT NULL,
  `stage` varchar(256) DEFAULT NULL,
  `reportType` varchar(256) DEFAULT NULL,
  `architectureDiagram` json DEFAULT NULL,
  `troubleshooting` json DEFAULT NULL,
  `isOperation` varchar(256) DEFAULT NULL,
  `problem` mediumtext,
  `groupLink` varchar(2048) DEFAULT NULL,
  `hasSLA` varchar(256) DEFAULT NULL,
  `isEffectSLA` varchar(256) DEFAULT NULL,
  `declineSLA` int(11) DEFAULT NULL,
  `slaDetail` json DEFAULT NULL,
  `effectSLO` json DEFAULT NULL,
  `budgetSLA` int(11) DEFAULT NULL,
  `isReplay` varchar(256) DEFAULT NULL,
  `participants` json DEFAULT NULL,
  `improvementFinishTime` datetime(6) DEFAULT NULL,
  `lastUpdateTime` datetime(6) DEFAULT NULL,
  `lastUpdateUser` varchar(256),
  `levelBasis` varchar(256),
  PRIMARY KEY (`id`),
  UNIQUE KEY `fid` (`fid`)
) ENGINE=InnoDB AUTO_INCREMENT=5173 DEFAULT CHARSET=utf8;
```

# 样例数据

```jsx

1511	202306260003	2023年6月20日 迁移VPC导致多个业务出现大量告警		已归档	2023年	2023年6月	2023年Q2	2023-06-20 05:30:00	2023-06-20 05:31:00	1	2023-06-20 05:31:00	0	2023-06-20 05:32:00	1	2023-06-20 05:35:00	3	5	[{"fid": "202306260003", "time": "2023-06-20 05:29", "image": [], "stage": "0", "content": "林明世联系华为操作 db vpc切换，操作完成无告警"}, {"fid": "202306260003", "time": "2023-06-20 05:37", "image": [], "stage": "4", "content": "一线电话联系告警相关业务运维（刘智敏，董富欣，郑佳裕）上线处理"}, {"fid": "202306260003", "time": "2023-06-20 05:37", "image": [], "stage": "4", "content": "林明世反映华为云的回退工具有问题正在处理中"}, {"fid": "202306260003", "time": "2023-06-20 05:37", "image": [], "stage": "4", "content": "一线同步SLI相关告警到群"}, {"fid": "202306260003", "time": "2023-06-20 05:38", "image": [], "stage": "4", "content": "刘智敏反映数据部那边有问题"}, {"fid": "202306260003", "time": "2023-06-20 05:39", "image": [], "stage": "4", "content": "郑佳裕开始排查数据部相关问题"}, {"fid": "202306260003", "time": "2023-06-20 05:44", "image": [], "stage": "4", "content": "一线同步SLI前端相关告警并联系黎超文进行处理"}, {"fid": "202306260003", "time": "2023-06-20 05:45", "image": [], "stage": "4", "content": "林明世反馈回退完成询问是否已经恢复"}, {"fid": "202306260003", "time": "2023-06-20 05:48", "image": [], "stage": "4", "content": "一线反馈仍有新的告警"}, {"fid": "202306260003", "time": "2023-06-20 05:49", "image": [], "stage": "4", "content": "林明世询问业务运维回退完成后是否还有问题"}, {"fid": "202306260003", "time": "2023-06-20 05:52", "image": [], "stage": "4", "content": "黎超文反映拨测前端的活动服务没有问题"}, {"fid": "202306260003", "time": "2023-06-20 06:01", "image": [], "stage": "4", "content": "刘智敏反映已恢复"}, {"fid": "202306260003", "time": "2023-06-20 06:02", "image": [], "stage": "4", "content": "一线询问数据部是否有问题告警仍在出现"}, {"fid": "202306260003", "time": "2023-06-20 06:03", "image": [], "stage": "4", "content": "郑佳裕反映数据服务没有问题，归因回传全链路群有kafka告警，联系相关同学排查处理"}, {"fid": "202306260003", "time": "2023-06-20 06:16", "image": [], "stage": "4", "content": "黎超文反馈还有些服务有耗时的告警，重启服务恢复"}, {"fid": "202306260003", "time": "2023-06-20 06:21", "image": [], "stage": "4", "content": "黎超文确认没有问题了"}, {"fid": "202306260003", "time": "2023-06-20 06:25", "image": [], "stage": "4", "content": "林明世反映今天操作7个vpc，因出现故障，db vpc回退，02 vpc暂停操作，合并到下次操作"}, {"fid": "202306260003", "time": "2023-06-20 05:31", "image": [], "stage": "1", "content": "一线注意到各个告警群开始出现大量告警反馈给林明世"}, {"fid": "202306260003", "time": "2023-06-20 05:31", "image": [], "stage": "2", "content": "一线注意到各个告警群开始出现大量告警反馈给林明世"}, {"fid": "202306260003", "time": "2023-06-20 05:32", "image": [], "stage": "3", "content": "林明世开始联系华为进行回退林明世开始联系华为进行回退"}, {"fid": "202306260003", "time": "2023-06-20 06:51", "image": [], "stage": "4", "content": "毕锦琪重启flink作业，恢复kafka数据"}]	第三方	变更引起	VPC	[]	华为切换对等连接，导致部分网络存在中断，且3分钟无法连接		["TT语音"]	非经济损失		P5	影响时间共5分钟，前段3分钟，回滚2分钟；占非核心全网20%以下	外部服务	有监控	有告警	首发	[]		运维定位	否	是	[]		[]	陈正		2023-06-20 07:19:00	未超时		2023-07-03 10:00:00	已超时	2023-07-03 10:00:00	未超时	无预案	江梓帆	T2754	已发布	故障	[]	[]	非运营类	<ul><li style="line-height: 1.5;">为什么会发生？</li><ul><li style="line-height: 1.5;">VPC基础网络变更变革导致长链接失效,且3分钟不可用</li><ul><li style="line-height: 1.5;">为什么3分钟不可用？</li><ul><li style="line-height: 1.5;">华为侧仍在定位原因。</li><ul><li style="line-height: 1.5;">action:持续跟进华为排查根因</li></ul></ul></ul></ul><li style="line-height: 1.5;">有没有及时发现？</li><ul><li style="line-height: 1.5;">有</li></ul><li style="line-height: 1.5;">各环节耗时是否过长？</li><ul><li style="line-height: 1.5;">故障恢复时间较长</li><ul><li style="line-height: 1.5;">为什么故障恢复时间较长？</li><ul><li style="line-height: 1.5;">华为侧回退工具有问题影响回退时长10分钟</li><ul><li style="line-height: 1.5;">aciton:跟进华为回退工具有效性。</li></ul><li style="line-height: 1.5;">部分业务需要重启恢复</li><ul><li style="line-height: 1.5;">为什么部分业务需要重启恢复？</li><ul><li style="line-height: 1.5;">因为应用程序使用长链接且没重试机制</li><ul><li style="line-height: 1.5;">action：应用程序添加长链接断开重试机制</li></ul></ul></ul></ul></ul></ul></ul>					[]	[]			[]				
1535	202306050001	2023年06月05日 打开腾讯集群的mtls导致多个功能出现异常	https://q9jvw0u5f5.feishu.cn/wiki/IdzkwaNPfiCYJfkvvgpcHBSmnZg	已归档	2023年	2023年6月	2023年Q2	2023-06-05 10:17:00	2023-06-05 10:20:00	3	2023-06-05 10:20:00	0	2023-06-05 10:26:00	6	2023-06-05 10:30:00	4	13	[{"fid": "202306050001", "time": "2023-06-05 10:17", "image": [], "stage": "0", "content": "郑琦涵打开了tc-bj-1-prod的mTLS，配置从DISABLE调整为PERMISSIVE\n- PERMISSIVE：负载同时接受mutual TLS和明文流量。\n- STRICT：负载仅接受mutual TLS流量。\n- DISABLE：禁用mutual TLS。"}, {"fid": "202306050001", "time": "2023-06-05 10:20", "image": [], "stage": "2", "content": "一线值班联系智敏和黄金开始排查"}, {"fid": "202306050001", "time": "2023-06-05 10:21", "image": [], "stage": "2", "content": "少华确认黄金在做变更"}, {"fid": "202306050001", "time": "2023-06-05 10:20", "image": [], "stage": "1", "content": "SLI/SLO告警群出现presentlogic成功率小于等于95%持续3分钟告警"}, {"fid": "202306050001", "time": "2023-06-05 10:20", "image": [], "stage": "1", "content": "SLI/SLO告警群开始大量出现腾讯ingressgateway-tt-api-internal 调用服务失败率 <= 92%告警"}, {"fid": "202306050001", "time": "2023-06-05 10:20", "image": [], "stage": "1", "content": "语音直播反馈群报障很多主播反馈直播的时候突然弹出没有直播权限"}, {"fid": "202306050001", "time": "2023-06-05 10:23", "image": [], "stage": "2", "content": "一线拉起故障处理群"}, {"fid": "202306050001", "time": "2023-06-05 10:26", "image": [], "stage": "3", "content": "黄金确认变更，开启了mTLS，由此引起"}, {"fid": "202306050001", "time": "2023-06-05 10:30", "image": [], "stage": "4", "content": "黄金回滚变更，一线运维拨测故障恢复"}]	配置不当	变更引起	容器	[]	tc-bj-1-prod集群开启了mTLS，导致请求异常	后端/服务端故障	["TT语音"]	非经济损失	已定级故障	P4	"进入腾讯集群的所有流量，影响占比：
核心功能，影响全网12%的用户，持续时间13分钟
非核心功能，影响全网40%的用户，持续时间13分钟"	外部服务	有监控	有告警	首发	["SLI告警"]	监控告警	运维定位	否	是	[]	"建立命名空间测试等相关验证,后续要按照变更规范，黑屏变更流程,k8s/mesh等变更列入重大变更"	[]	董富欣		2023-06-05 10:58:00	未超时		2023-06-13 18:00:00	未超时	2023-06-13 18:00:00	未超时	无预案			已发布	故障	[]	[]	非运营类	<ul><li style="line-height: 1.5;">为什么会发生？（必填）</li><ul><li style="line-height: 1.5;">tc-bj-1-prod集群开启mTLS模式从DISABLE调整为PERMISSIVE导致出现故障</li><ul><li style="line-height: 1.5;">后续的计划：</li><ul><li style="line-height: 1.5;">查明具体原因，确定方案，以及后续操作</li><li style="line-height: 1.5;">action：建立命名空间测试</li></ul></ul></ul><li style="line-height: 1.5;">是否做个测试验证？</li><ul><li style="line-height: 1.5;">做过，测试正常</li></ul><li style="line-height: 1.5;">K8S层面影响范围有哪些？</li><ul><li style="line-height: 1.5;">影响tc-bj-1-prod集群内，网关与Pod，Pod与Pod之间的新建链接通信，已建立链接不受影响，可能部分现在链接受影响，后续查询监控确认现存链接</li></ul><li style="line-height: 1.5;">是否有变更事件通知？</li><ul><li style="line-height: 1.5;">缺少</li><ul><li style="line-height: 1.5;">有发起工单，但未通过，进行了变更</li><li style="line-height: 1.5;">为什么没通过，却进行变更？</li><ul><li style="line-height: 1.5;">口头约定</li><ul><li style="line-height: 1.5;">action：后续要按照变更规范，黑屏变更流程</li></ul></ul></ul></ul></ul>					[]	[]			[]				
1536	202305130001	2023年05月13日 Istio全局证书过期导致多个产品的大部分功能不可用	https://q9jvw0u5f5.feishu.cn/wiki/I0Mhw9u5FiYgYUk15xacd019ngd#	已归档	2023年	2023年5月	2023年Q2	2023-05-13 16:04:00	2023-05-13 16:06:00	2	2023-05-13 16:08:00	2	2023-05-13 17:22:00	74	2023-05-13 18:09:00	47	125	[{"fid": "202305130001", "time": "2023-05-13 16:04", "image": [], "stage": "0", "content": "证书过期"}, {"fid": "202305130001", "time": "2023-05-13 17:22", "image": [], "stage": "3", "content": "陈骋发现证书链cert-chain.pem中第二段istio root ca过期"}, {"fid": "202305130001", "time": "2023-05-13 17:30", "image": [], "stage": "3", "content": "吴畏到达公司"}, {"fid": "202305130001", "time": "2023-05-13 17:34", "image": [], "stage": "3", "content": "张登强查看证书，cert-chain.pem 里面有三段，前后都没问题，但是中间那段过期"}, {"fid": "202305130001", "time": "2023-05-13 17:50", "image": [], "stage": "3", "content": "吴畏重新签发证书，并在海外集群测试，发现服务恢复正常"}, {"fid": "202305130001", "time": "2023-05-13 16:08", "image": [], "stage": "2", "content": "一线运维联系到刘鹏华排查"}, {"fid": "202305130001", "time": "2023-05-13 16:10", "image": [], "stage": "2", "content": "SLI群OBS上传下载失败告警，一线联系刘智敏查看"}, {"fid": "202305130001", "time": "2023-05-13 16:12", "image": [], "stage": "2", "content": "刘智敏看istio-ingressgateway-external-obs调用obs-cdn-http-logic不通，怀疑和前天的故障一样，联系黄金和郑琦涵排查"}, {"fid": "202305130001", "time": "2023-05-13 16:14", "image": [], "stage": "2", "content": "《用户反馈突增告警群》内触发用户报障告警，反馈内容为：“头像更换不了，头像变白”；同时客服也收到多例用户反馈头像显示异常"}, {"fid": "202305130001", "time": "2023-05-13 16:15", "image": [], "stage": "2", "content": "一线运维发起群聊邀请研发人员与业务运维人员进群排查"}, {"fid": "202305130001", "time": "2023-05-13 16:16", "image": [], "stage": "2", "content": "黄金在内部群安排郑奇涵盘查是否和istio版本有关"}, {"fid": "202305130001", "time": "2023-05-13 16:18", "image": [], "stage": "2", "content": "用户反馈突增告警群》内触发用户报障告警，反馈内容为：“tt卡,异常反馈,房间异常,卡顿掉线”等"}, {"fid": "202305130001", "time": "2023-05-13 16:19", "image": [], "stage": "2", "content": "中台反馈问题"}, {"fid": "202305130001", "time": "2023-05-13 16:20", "image": [], "stage": "2", "content": "黄金重启网关（重启没有成功），故障没恢复，查看错误和证书有关，没有同步。同时服务Pod开始出现大量退出重启"}, {"fid": "202305130001", "time": "2023-05-13 16:24", "image": [], "stage": "2", "content": "刘智敏查看异常重启的pod，发现istio-proxy启动报错，联系陈骋协助排查"}, {"fid": "202305130001", "time": "2023-05-13 16:31", "image": [], "stage": "2", "content": "陈骋排查发现istiod 证书过期，需要吴畏上线协助，一线运维人员电联吴畏上线协助处理"}, {"fid": "202305130001", "time": "2023-05-13 16:31", "image": [], "stage": "2", "content": "刘智敏发现除了istio-ingressgateway-external-obs网关外，其他入口网关也出现调用不通的情况"}, {"fid": "202305130001", "time": "2023-05-13 16:34", "image": [], "stage": "2", "content": "联系上吴畏，吴畏表示证书相关文件在公司，需要到公司处理"}, {"fid": "202305130001", "time": "2023-05-13 16:42", "image": [], "stage": "2", "content": "黄金，在内部安排张登强排查根证书是否过期，打算重新签名证书，发现无法操作（缺少证书信息和私钥），郑奇涵反馈版本没有问题"}, {"fid": "202305130001", "time": "2023-05-13 16:43", "image": [], "stage": "2", "content": "黄金怀疑根证书过期，发起视频会议进行信息同步和排查"}, {"fid": "202305130001", "time": "2023-05-13 16:49", "image": [], "stage": "2", "content": "朱少华提出关闭ssl认证"}, {"fid": "202305130001", "time": "2023-05-13 16:50", "image": [], "stage": "2", "content": "陈骋排查根证书没有过期 ，需要排查其他证书"}, {"fid": "202305130001", "time": "2023-05-13 16:51", "image": [], "stage": "2", "content": "货币和海外开始反馈业务无法使用"}, {"fid": "202305130001", "time": "2023-05-13 17:11", "image": [], "stage": "2", "content": "黄金初步排查确定是证书问题"}, {"fid": "202305130001", "time": "2023-05-13 16:06", "image": [], "stage": "1", "content": "《T盾问题反馈群》反馈一审快速审核视频加载不出来"}, {"fid": "202305130001", "time": "2023-05-13 17:20", "image": [], "stage": "2", "content": "张登强在内部群反馈证书确实没有过期的， 只关注rootCA和clusterCA，未能检查到问题"}, {"fid": "202305130001", "time": "2023-05-13 18:03", "image": [], "stage": "3", "content": "吴畏将证书部署到TT生产集群、统一集群、TTweb集群和中台集群，业务逐步恢复正常"}, {"fid": "202305130001", "time": "2023-05-13 18:09", "image": [], "stage": "4", "content": "一线运维拨测线上客户端各个功能均正常"}]	配置不当	非变更引起	Istio	["流程规范", " 系统工具"]	istio 根证书过期，引起k8S集群内部调用失败，导致客户端大部分功能无法使用	后端/服务端故障	["TT语音", "声洞"]	间接经济损失	已定级故障	P1	线上客户端卡顿，无法发送消息，无法登录，无法进房，无法加载图片；内部T盾图片无法加载，运营后台活动资源无法上传	外部服务	有监控	有告警	非首发	["非首发"]	运营/产品/公关	研发定位	是	否	[]	"通过脚本检查证书过期时间,证书管理系统,缩短从故障开始到发送的时间,增加 istio 监控指标,Root CA & Istio Root CA暂行管理办法,自签名证书管理机制建立（合并原PKI建设）,故障上升机制完善,告警泛滥-告警收敛,告警看板,HPA 拉长缩扩容的时间,xDS 下发问题-单独会议讨论方案,应用层面超时问题，导致稳定性问题（影响SLO）- 配置兜底超时,盘点当前的集群稳定性风险,证书快速恢复预案"	["黄金", "吴畏"]	刘智敏	刘俊杰	2023-05-13 17:05:00	未超时	2023-05-15 00:00:00	2023-05-15 10:00:00	未超时	2023-05-24 11:00:00	未超时	无预案			已发布	故障	[]	[]	非运营类	<ul><li style="line-height: 1.5;">为什么会发生？（必填）</li><ul><li style="line-height: 1.5;">证书签名脚本参数错误，原计划期限30年，结果只有1年</li><ul><li style="line-height: 1.5;">操作是否有double check</li><ul><li style="line-height: 1.5;">有，只检查了证书配置，未检查具体证书的有效期限</li><li style="line-height: 1.5;">问题：缺乏证书管理系统</li><ul><li style="line-height: 1.5;">action ：</li><ul><li style="line-height: 1.5;">短期：1. 通过脚本检查证书过期时间</li><li style="line-height: 1.5;">长期：1. 证书管理系统（PKI）</li></ul></ul><li style="line-height: 1.5;">问题：内部证书是否只做提醒，不做强制过期，是否可行？</li><ul><li style="line-height: 1.5;">action：无，需要继续讨论方案是否可行（出于安全方面考虑）</li></ul><li style="line-height: 1.5;">问题：内部通讯是否需要这么高的安全级别？</li><ul><li style="line-height: 1.5;">action：需要</li></ul></ul></ul></ul><li style="line-height: 1.5;">有没有及时发现？</li><ul><li style="line-height: 1.5;">及时发现。但非首发</li><ul><li style="line-height: 1.5;">action：缩短告警从故障开始到发生的时间</li></ul><li style="line-height: 1.5;">是否可以从基础架构发现大面积问题？</li><ul><li style="line-height: 1.5;">action：从 istio 监控指标层面入手</li></ul></ul><li style="line-height: 1.5;">各环节耗时是否过长？</li><ul><li style="line-height: 1.5;">定位时长过长</li><ul><li style="line-height: 1.5;">为什么没有快速定位到实际过期的证书？</li><ul><li style="line-height: 1.5;">未能在开始确定证书链有3个证书，忽略了中间证书</li><ul><li style="line-height: 1.5;">证书签发时间过长，遗忘最初架构</li><ul><li style="line-height: 1.5;">问题：排查过程已排查到证书问题，未做同步到故障排查群</li><ul><li style="line-height: 1.5;">action：排查问题需要做同步，防止信息不同步，重复排查。</li></ul></ul></ul></ul></ul></ul><li style="line-height: 1.5;">证书过期是否有监控告警？</li><ul><li style="line-height: 1.5;">问题：当前没有</li><ul><li style="line-height: 1.5;">action：</li><ul><li style="line-height: 1.5;">短期：1. 盘点证书列表，做证书监控</li><li style="line-height: 1.5;">长期：1. 基于系统的证书管理机制（生成、分发、管理、预警）</li></ul></ul></ul><li style="line-height: 1.5;">恢复时长过长</li><ul><li style="line-height: 1.5;">尝试其他手段失败（关闭 MTLS），由于证书过期 webhook 无法认证通过</li><li style="line-height: 1.5;">证书签发时间过长，遗忘步骤</li><ul><li style="line-height: 1.5;">action：需要具备标准的操作文档</li></ul><li style="line-height: 1.5;">海外验证也需要时间（5-6分钟）</li><li style="line-height: 1.5;">问题：为什么只有一个人可以管理证书？</li><ul><li style="line-height: 1.5;">私钥不应该由人来管理，需要系统。</li><ul><li style="line-height: 1.5;">证书谁来管理？</li><ul><li style="line-height: 1.5;">证书由运维来管理</li><ul><li style="line-height: 1.5;">action：</li><ul><li style="line-height: 1.5;">短期：</li><ul><li style="line-height: 1.5;">证书由运维管理</li><li style="line-height: 1.5;">增加备岗机制最少由2个人来管理</li><li style="line-height: 1.5;"> 建立证书管理机制（人工流程）</li></ul><li style="line-height: 1.5;"> 长期：</li><ul><li style="line-height: 1.5;">长期：基于系统的证书管理机制（生成、分发、管理、预警）系统工具层面管理</li></ul></ul></ul></ul></ul></ul></ul><li style="line-height: 1.5;">没什么没有及时上升？</li><ul><li style="line-height: 1.5;">问题：是有上升机制的，未按照上升机制执行</li><ul><li style="line-height: 1.5;">action：</li><ul><li style="line-height: 1.5;">1. 故障发生时，需要严格按照上升机制执行</li><li style="line-height: 1.5;"> 2. 上升内容需要再明确和完善（从业务层面描述影响范围）</li><li style="line-height: 1.5;"> 3. 明确故障处理协调人的职责</li></ul></ul></ul><li style="line-height: 1.5;">告警泛滥</li><ul><li style="line-height: 1.5;">问题：告警泛滥</li><ul><li style="line-height: 1.5;"> action：</li><li style="line-height: 1.5;">长期：告警收敛 </li><li style="line-height: 1.5;">短期：告警看板，需要重新开启</li></ul></ul><li style="line-height: 1.5;">是否还存在其他影响集群稳定性的风险？</li><ul><li style="line-height: 1.5;"> 风险：xDS 下发问题（集群规模大）</li><li style="line-height: 1.5;"> action：</li><ul><li style="line-height: 1.5;"> 短期：拉长缩扩容的时间</li><li style="line-height: 1.5;">长期：单独会议讨论方案</li></ul><li style="line-height: 1.5;"> 风险：应用层面超时问题，导致稳定性问题（影响SLO）</li><li style="line-height: 1.5;"> action：</li><ul><li style="line-height: 1.5;"> 短期：配置兜底超时（dr）</li><li style="line-height: 1.5;"> 长期：容器云平后可以解决该问题</li></ul><li style="line-height: 1.5;"> action：盘点当前的稳定性风险（监控）</li></ul><li style="line-height: 1.5;">Istio 风险越来越大，收益（降级、熔断、故障切换）未能体现。如何继续推进业务适配服务网格，发挥istio的优势。</li><ul><li style="line-height: 1.5;">证书快速恢复预案</li><ul><li style="line-height: 1.5;">action：形成证书故障恢复预案</li></ul></ul></ul>					[]	[]			[]				
```

# **技术方案：基于DDD与LangGraph的大禹平台相似故障检索系统**

## **第一部分：RAG数据注入与知识索引管道**

本节将详细阐述关键的ETL（Extract, Transform, Load）流程。RAG应用的成败完全取决于向量索引中数据的质量 。鉴于

`fault_info` 表的复杂性，该数据管道是本项目中技术挑战最大的部分。

### **1.1. 从MySQL进行高性能数据抽取**

我们需要在不影响生产系统性能的前提下，从只读的MySQL数据库中抽取数据。

- **策略：基于水印的增量加载:** 每次运行时都进行全表扫描是低效且不可扩展的。我们将实施一种增量加载策略。由于表中存在 `lastUpdateTime` 字段，我们可以采用“水印”（Watermarking）机制 **14**。数据管道将记录并持久化上一次成功处理的最后一条记录的
    
    `lastUpdateTime` 值。在后续的运行中，管道将只查询 `lastUpdateTime` 大于该水印值的记录。
    
- **首次全量加载:** 管道的首次运行将执行一次性的全量加载，以索引所有历史故障数据。此后的所有运行都将是增量的。
- **韧性设计:** 数据管道必须具备高韧性。通过引入检查点（Checkpointing）和健全的错误处理机制，管道能够在发生故障后从上一个成功的检查点恢复，而无需重新处理已经成功注入的数据，确保了流程的幂等性和效率 **14**。

### **1.2. 战略性数据转换与富化：复合分块策略**

这是知识准备阶段的核心。任何简单的、一刀切的分块（Chunking）方法都将导致失败。我们必须采用一种复合的、字段感知的策略：首先将结构化数据序列化为连贯的自然语言文本，然后对生成的长文本进行分块。

`fault_info` 表本质上是多种数据形态的混合体。将 `faultDescribe`（自然语言描述）、`playback`（JSON格式的时间线）和 `problem`（HTML格式的逻辑树）等字段同等对待，是一个会导致严重信息损失的错误。

RAG的目标是检索语义相关的 *上下文*。`playback` 字段的语义蕴含在事件之间的 *时序关系* 中；`problem` 字段的语义则在于“五个为什么”分析的 *因果关系* 链条中。一个简单的文本分割器会无情地切碎这些至关重要的关系，例如，将一个“为什么？”（Why?）和它的“措施”（Action:）分割到两个毫无关联的文本块中，从而完全破坏其语义。

因此，转换（Transform）必须在分块（Chunking）之前进行。我们必须先将这些结构化、半结构化的数据转换成一种叙事性的文本格式，然后再交由文本分割器进行安全的处理。这本质上是一种基于文档结构的分块方法 **15**。具体流程如下：对数据库中的每一条故障记录，通过组合其关键字段的序列化版本，生成一个单一的“故障描述文档”。然后，对这个生成的文档应用标准的文本分块方法，如

`RecursiveCharacterTextSplitter`。

**表3：字段特定的分块与处理策略**

| 字段名称 | 数据类型 | 预处理 / 序列化逻辑 | 序列化后文本示例 | 分块策略 |
| --- | --- | --- | --- | --- |
| `faultDescribe` | `varchar` | 无需处理，直接使用。 | "2023年6月20日 迁移VPC导致多个业务出现大量告警" | 作为主文档的一部分。 |
| `playback` | `json` | 遍历JSON数组。对每个对象，格式化为："事件回放于 {time}: {content}。" 将所有字符串拼接。 | "事件回放于 2023-06-20 05:29: 林明世联系华为操作 db vpc切换。事件回放于 2023-06-20 05:37: 一线电话联系告警相关业务运维。" | 作为主文档的一部分。 |
| `problem` | `mediumtext` (HTML) | 解析HTML的 `<ul>`/`<li>` 结构。遍历嵌套列表，将“五个为什么”的逻辑树重构为一段连贯的段落。 | "根本原因分析如下：问题 '为什么会发生？' 的答案是 'VPC基础网络变更导致长链接失效'。进一步追问 '为什么故障恢复时间较长？'，原因是 '华为侧回退工具有问题' 以及 '部分业务需要重启恢复'。" | 作为主文档的一部分。 |
| `causeDescribe` | `longtext` | 无需处理，直接使用。 | "华为切换对等连接，导致部分网络存在中断..." | 作为主文档的一部分。 |
| `improveAction` | `json` | 遍历JSON数组。格式化为："改进措施：{content}。" 将所有字符串拼接。 | "改进措施：应用程序添加长链接断开重试机制。改进措施：跟进华为回退工具有效性。" | 作为主文档的一部分。 |
| `level`, `causeClassify`, `serviceClassification` | `varchar` | 组合成一个单一的元数据字符串，用于过滤和增强检索。 | "故障级别：P5。原因分类：第三方。影响业务：TT语音。" | 作为每个文本块的元数据存储，不包含在被向量化的文本本身。 |
- **最终的文本块:** 序列化完成后，所有生成的文本将被拼接成一个代表单次故障的完整文档。随后，这个文档将使用 `RecursiveCharacterTextSplitter` 进行分割 **15**。该方法能有效尊重段落、句子等自然语言的边界。最终生成的每一个文本块（chunk）都将与原始的
    
    `fid` 以及其他关键元数据（如故障级别、原因分类等）相关联，以便在检索后进行精确的引用和过滤。
    

### **1.3. 嵌入与向量存储架构**

- embedding模型

```jsx
from typing import List

import requests
from langchain.embeddings.base import Embeddings
from pydantic import BaseModel, Field

class Qwen3EmbeddingConfig(BaseModel):
    base_url: str = Field(default="http://ray.ttyuyin.com:10001/ray-qwen3-emb")
    model_name: str = Field(default="Qwen/Qwen3-Embedding-4B", description="模型名称")

class Qwen3Embedding(Embeddings):
    def __init__(self, config: Qwen3EmbeddingConfig = Qwen3EmbeddingConfig(), is_query: bool = False):
        self.config = config
        self.is_query = is_query
        self.batch_size = 50

    def _get_embedding(self, texts: List[str]) -> List[float]:
        url = f"{self.config.base_url}/v1/embeddings"

        payload = {
            "model": self.config.model_name,
            "input": texts,
            "encoding_format": "float",
            "is_query": self.is_query,
        }

        response = requests.post(url, json=payload)
        response.raise_for_status()

        return response.json()["data"]

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """Embed a list of documents using Qwen3."""
        data = []
        for i in range(0, len(texts), self.batch_size):
            data.extend(self._get_embedding(texts[i : i + self.batch_size]))
        return [d["embedding"] for d in data]

    def embed_query(self, text: str) -> List[float]:
        """Embed a query using Qwen3."""
        data = self._get_embedding([text])
        return data[0]["embedding"]

```

- 向量存储

使用milvus

链接信息:

```jsx

[milvus]
uri = "https://in01-55c8dcbceba5eb8.tc-ap-beijing.vectordb.zilliz.com.cn:443"
user = "yw_ops"
db_name = "yw_ops_db"
timeout = 20
index_params = { metric_type = "COSINE", index_type = "FLAT"}
search_params = { metric_type = "COSINE"}
vector_dim = 2560
```

## **第二部分：系统架构与LangGraph编排**

本节将详细阐述系统的运行时架构，重点关注如何使用LangGraph来编排各个组件以响应用户查询。选择LangGraph的原因在于其有状态的、可循环的图结构，允许我们构建超越简单线性RAG链的、复杂的、类似代理（Agent）的行为 **25**。

### **2.1. 整体系统架构概览**

系统端到端的流程如下图所示：

用户 -> UI/API -> LangGraph编排器 -> [查询路由节点] -> [相似性检索路径 | 问答检索路径] -> LLM综合节点 -> 响应 -> 用户

在此流程中，LangGraph编排器将与milvus向量存储以及源MySQL数据库（通过 `FaultRepository`）进行交互，动态地执行任务。

### **2.2. 设计LangGraph状态机**

LangGraph应用的核心是 `StateGraph`。我们将定义一个中心化的状态对象，该对象在图的各个节点之间传递，并在每一步被更新 **25**。

- **状态定义 (`TypedDict`):**
    
    **Python**
    
    ```
    from typing import TypedDict, List, Literal, Optional, Annotated
    import operator
    from langchain_core.documents import Document
    
    class FaultIntelligenceState(TypedDict):
        # 用户原始输入
        user_query: str
        # 经路由节点分析后的查询类型
        query_type: Literal["similarity_search", "qa", "unknown"]
        # 仅在相似性搜索时使用，目标故障的ID
        target_fid: Optional[str]
        # 从向量数据库检索到的文档块
        retrieved_docs: List
        # 格式化后，准备送入LLM的上下文
        fault_context: str
        # 记录Agent执行步骤的日志，用于追踪和调试
        analysis_steps: Annotated[List[str], operator.add]
        # 最终生成的响应
        final_response: Optional[str]
        # 错误信息
        error_message: Optional[str]
    
    ```
    
- **图初始化:** `graph = StateGraph(FaultIntelligenceState)`

### **2.3. 使用节点和条件边构建Agentic工作流**

我们将构建一个能够根据用户意图进行智能路由的图。这是LangGraph条件边（Conditional Edges）功能的直接应用 **25**。

- **节点1: `route_query` (入口节点)**
    - **功能:** 此节点是图的入口点，负责分析状态中的 `user_query`。它会利用一个大语言模型（LLM）的函数调用（Function Calling）能力或特定的启发式规则来判断用户的意图，并设定 `query_type`。例如，如果查询匹配 "查找与 {fid} 相似的故障" 这样的模式，它会将 `query_type` 设为 `similarity_search` 并提取出 `target_fid`。如果是一个泛泛的问题，则设为 `qa`。
    - **输出:** 更新状态中的 `query_type` 和 `target_fid` 字段。
- **从 `route_query` 出发的条件边:**
    - **功能:** 一个简单的路由函数，读取 `state['query_type']` 的值。
    - **映射关系:**
        - 如果值为 `"similarity_search"`，则路由到 `get_target_fault` 节点。
        - 如果值为 `"qa"`，则路由到 `retrieve_relevant_faults` 节点。
        - 如果值为 `"unknown"`，则路由到 `handle_error` 节点。
- **节点2a: `get_target_fault` (相似性搜索路径)**
    - **功能:** 此节点仅在相似性搜索路径上被激活。它使用 `FaultRepository` 从MySQL数据库中获取 `target_fid` 对应的完整故障记录。然后，它执行与数据注入管道中完全相同的序列化流程，为目标故障创建一个权威的、富文本的表示。
    - **输出:** 将状态中的 `user_query` 字段更新为这个新生成的、序列化后的故障文本。这一步巧妙地将一个“相似性搜索”任务转换成了一个标准的“文本检索”任务。
    - **下一条边:** 一条普通的、无条件的边 (`add_edge`)，指向 `retrieve_relevant_faults` 节点。
- **节点2b / 3: `retrieve_relevant_faults` (核心检索节点)**
    - **功能:** 此节点是两条主要路径的汇合点。它接收 `user_query`（可能是用户的原始问题，也可能是上一步序列化后的目标故障文本），使用 `gte-large-zh` 模型为其创建向量嵌入，然后查询ChromaDB以获取Top-K个最相似的文档块。
    - **输出:** 将检索到的文档块填充到状态的 `retrieved_docs` 列表中。
- **节点4: `synthesize_response` (响应综合节点)**
    - **功能:** 此节点将 `retrieved_docs` 和原始的 `user_query` 组合起来，构建一个详细的提示（Prompt），并将其发送给一个强大的生成式LLM（例如，来自**38**的Qwen或Yi系列模型）。提示将明确指示模型执行以下两种任务之一：(a) 如果是相似性搜索，列出相似的故障，并附上
        
        `fid` 和简要总结；(b) 如果是问答，*仅* 基于所提供的上下文材料来回答用户的问题。
        
    - **输出:** 将LLM生成的最终答案填充到状态的 `final_response` 字段。
    - **下一条边:** 一条普通的边，指向特殊的 `END` 节点，表示流程结束。
- **节点5: `handle_error` (错误处理节点)**
    - **功能:** 当无法理解用户意图时，此节点被激活。它会生成一个友好的错误提示信息，例如：“抱歉，我无法理解您的请求。您可以提问关于历史故障的问题，或者要求查找与特定故障ID相似的案例。”
    - **输出:** 将错误信息填充到 `error_message` 和 `final_response` 字段。
    - **下一条边:** 一条普通的边，指向 `END` 节点。

这种基于LangGraph的Agentic设计，相比简单的线性RAG链，具有显著的优越性。用户的需求是双重的：相似性搜索和开放式问答。一个线性的处理链难以优雅地同时满足这两种需求，通常需要复杂的提示工程来引导LLM理解不同的任务模式。而LangGraph允许我们通过条件边 **25** 明确地为这两种需求建立独立的处理路径，

`route_query` 节点就像一个智能的“调度中心”。

特别是对于相似性搜索，`get_target_fault -> retrieve_relevant_faults` 的流程是一个非常强大的模式。它没有尝试用一个简单的查询去捕捉“相似性”这一模糊概念，而是先获取目标故障的完整、权威的文本表示，然后用这个信息极其丰富的长文本作为查询向量。这为向量数据库提供了比用户简短输入强得多的语义信号，从而极大地提高了检索的准确性。此外，这种模块化的图结构也具备极高的可扩展性。例如，我们可以轻易地在 `synthesize_response` 节点后增加一个条件边，引入“人在回路”（Human-in-the-loop）的审核节点 **29**，允许领域专家在最终答案返回给用户前进行校对和批准。

## **第三部分：接口、部署与运维就绪**

本最后部分将涵盖将系统交付给用户并确保其在生产环境中可靠运行的实际问题。

### **4.1. 设计MCP工具REST API**

为了实现服务间的集成，我们将定义一个清晰、规范的RESTful API。FastAPI是实现该API的绝佳选择，因为它具备高性能、自动生成API文档（Swagger UI）以及基于Pydantic的强大数据验证能力 **30**。

**表4：MCP工具API端点规范**

| 端点 (Endpoint) | HTTP 方法 | 请求体 (JSON Schema) | 成功响应 (200 OK) | 错误响应 (4xx/5xx) |
| --- | --- | --- | --- | --- |
| `/v1/query` | `POST` | `{"query": "string"}` | `{"response": "string", "source_fids": ["string"]}` | `{"error": "string"}` |
| `/v1/faults/{fid}/similar` | `GET` | N/A | `{"similar_faults": [{"fid": "string", "description": "string", "similarity_score": "float"}]}` | `{"error": "string", "detail": "Fault not found"}` |

一个正式的API规范是专业软件开发的基石。它定义了服务提供者与消费者之间的契约。这使得前端团队或其他依赖本服务的团队可以立即开始他们的集成开发工作，他们可以基于此规范构建一个模拟服务器（Mock Server），而无需等待后端服务的完整实现。

- **认证机制:** API必须是安全的。推荐采用标准的基于令牌的认证机制，例如在HTTP请求头中传递API密钥（API Key）或使用OAuth2的承载令牌（Bearer Token）。

### **4.2. 使用Streamlit构建快速验证前端**

对于用于验证和演示的前端界面，Streamlit是理想的选择。它允许数据科学家和后端工程师仅使用Python代码就能快速构建交互式Web应用，完全无需前端开发经验 **31**。

- **UI组件规划:**
    - `st.title()`: 设置应用标题 "大禹平台 - 相似故障查询" **31**。
    - `st.text_input()`: 提供一个文本输入框，供用户输入自然语言问题或故障 `fid` **32**。
    - `st.button()`: 一个“提交”按钮，用于触发查询 **31**。
    - `st.spinner()`: 在后端处理请求时，显示一个加载指示器，提升用户体验 **33**。
    - `st.markdown()`: 用于展示由LangGraph Agent生成的、格式化后的最终答案。
    - `st.expander()`: 提供一个可展开的区域，用于选择性地展示生成答案所依据的源文档（包括 `fid` 和内容摘要），这极大地增强了系统的透明度和可信度 **34**。

### **4.3. 部署与MLOps策略**

- **容器化:** 整个应用，包括FastAPI服务器、Streamlit应用以及数据注入管道，都将被容器化（Dockerize）。这确保了在不同环境（开发、测试、生产）中的一致性和可移植性。
- **CI/CD:** 建立一套持续集成/持续部署（CI/CD）流水线（例如，使用GitHub Actions或Jenkins）。该流水线将自动化执行代码测试、构建Docker镜像，并将其部署到像Kubernetes这样的容器编排平台。
- **可观测性:** 对于LLM应用，传统的CPU、内存监控是远远不够的。我们需要LLM应用特有的可观测性能力。我们将集成 **LangSmith** **35**，它与LangGraph紧密耦合。LangSmith能够让我们追踪图执行的每一步，检查中间状态，并调试Agent的行为。对于我们设计的这种复杂的、带条件分支的工作流来说，这种能力是无价的。
- **索引同步:** 基于水印机制的数据注入管道，将被配置为一个周期性任务（例如，使用Kubernetes CronJob），每小时运行一次。这确保了向量数据库中的知识能够与源MySQL数据库保持合理的同步，从而保证RAG系统知识库的时效性 **13**。