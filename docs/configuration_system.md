# 大禹故障RAG系统配置管理文档

## 概述

大禹故障RAG系统采用多层次、多环境的配置管理架构，支持灵活的配置方式和环境隔离。配置系统基于以下核心原则设计：

- **分层配置**：YAML配置文件 + 环境变量 + .env文件
- **环境隔离**：开发、测试、生产环境独立配置
- **安全性**：敏感信息通过环境变量管理
- **灵活性**：支持运行时配置覆盖
- **类型安全**：基于Pydantic的强类型配置验证

## 配置架构

### 1. 配置层次结构

```
配置优先级（高到低）：
1. 环境变量 (DAYU_* 前缀)
2. .env 文件
3. 代码中的默认值
```

**重要说明**：我们已经统一使用 pydantic_settings 管理所有配置，YAML配置文件不再用于运行时配置读取，仅作为环境特定的配置说明和文档。

### 2. 文件结构

```
project/
├── config/
│   ├── development.yaml    # 开发环境配置说明（非运行时读取）
│   └── production.yaml     # 生产环境配置说明（非运行时读取）
├── src/dayu_fault_rag/config/
│   └── settings.py         # 配置管理核心代码（pydantic_settings）
├── .env                    # 本地环境变量（可选）
└── env.example            # 环境变量模板
```

## 配置使用方式

### 1. YAML配置文件（仅作配置说明）

**重要**：YAML配置文件现在仅用作配置文档和环境说明，不在运行时读取。所有配置都通过 pydantic_settings 和环境变量管理。

#### 开发环境配置说明 (`config/development.yaml`)

此文件说明开发环境的推荐配置：

```yaml
# 开发环境配置说明
environment: development
debug: true

# 数据库配置 - 开发环境
database:
  # 通过环境变量配置：DAYU_MYSQL_HOST=localhost
  # 通过环境变量配置：DAYU_MYSQL_PORT=3306
  # 通过环境变量配置：DAYU_MYSQL_USERNAME=root
  # 通过环境变量配置：DAYU_MYSQL_PASSWORD=""
  database: itmp  # DAYU_MYSQL_DATABASE=itmp
  charset: utf8mb4
  pool_size: 5
  max_overflow: 10

# Milvus配置 - 开发环境  
milvus:
  # 通过环境变量配置：DAYU_MILVUS_HOST=localhost
  # 通过环境变量配置：DAYU_MILVUS_PORT=19530
  collection_name: fault_vectors_dev  # DAYU_MILVUS_COLLECTION
  dimension: 768
```

#### 生产环境配置说明 (`config/production.yaml`)

此文件说明生产环境的推荐配置：

```yaml
# 生产环境配置说明
# 所有敏感信息都通过环境变量配置

environment: production  # DAYU_ENVIRONMENT=production
debug: false            # DAYU_DEBUG=false

# 数据库配置 - 生产环境
database:
  # 主机、端口、用户名、密码通过环境变量配置：
  # DAYU_MYSQL_HOST, DAYU_MYSQL_PORT, DAYU_MYSQL_USERNAME, DAYU_MYSQL_PASSWORD
  database: itmp_sys  # DAYU_MYSQL_DATABASE=itmp_sys
  pool_size: 20
  max_overflow: 50

# Milvus配置 - 生产环境
milvus:
  # DAYU_MILVUS_HOST, DAYU_MILVUS_PORT, DAYU_MILVUS_USERNAME, DAYU_MILVUS_PASSWORD
  collection_name: fault_vectors_prod  # DAYU_MILVUS_COLLECTION=fault_vectors_prod
```

**新的配置方式说明：**
- 所有配置都通过环境变量（DAYU_前缀）或代码默认值管理
- YAML文件仅作为配置文档，说明不同环境的推荐设置
- 敏感信息完全通过环境变量管理，提高安全性
- 统一使用 pydantic_settings，避免重复的配置处理逻辑

### 2. 环境变量配置

#### 环境变量命名规范

所有环境变量使用 `DAYU_` 前缀，采用大写字母和下划线命名：

```bash
# 数据库配置
DAYU_MYSQL_HOST=localhost
DAYU_MYSQL_PORT=3306
DAYU_MYSQL_USERNAME=root
DAYU_MYSQL_PASSWORD=your_password
DAYU_MYSQL_DATABASE=itmp

# Milvus配置
DAYU_MILVUS_HOST=localhost
DAYU_MILVUS_PORT=19530
DAYU_MILVUS_COLLECTION=fault_vectors

# API配置
DAYU_API_HOST=0.0.0.0
DAYU_API_PORT=8000
DAYU_API_DEBUG=false

# 向量化配置
DAYU_EMBEDDING_BASE_URL=http://ray.ttyuyin.com:10001/ray-qwen3-emb
DAYU_EMBEDDING_MODEL=Qwen/Qwen3-Embedding-4B
DAYU_EMBEDDING_TIMEOUT=30

# 日志配置
DAYU_LOG_LEVEL=INFO
DAYU_LOG_FILE=/var/log/dayu-fault-rag/app.log
```

#### .env文件示例

项目根目录提供了完整的环境变量模板文件 `env.example`，包含所有可配置的环境变量：

```bash
# 复制模板文件并编辑
cp env.example .env
# 然后编辑 .env 文件设置具体的值
```

模板文件包含以下配置组：
- 基础环境配置（DAYU_ENVIRONMENT, DAYU_DEBUG等）  
- MySQL数据库配置（DAYU_MYSQL_*）
- Milvus向量数据库配置（DAYU_MILVUS_*）
- API服务配置（DAYU_API_*）
- 向量化服务配置（DAYU_EMBEDDING_*）
- 日志配置（DAYU_LOG_*）
- 可选的ETL和监控配置

**开发环境示例：**
```bash
DAYU_ENVIRONMENT=development
DAYU_DEBUG=true
DAYU_MYSQL_HOST=localhost
DAYU_MYSQL_PASSWORD=your_dev_password
DAYU_LOG_LEVEL=DEBUG
```

**生产环境示例：**
```bash
DAYU_ENVIRONMENT=production
DAYU_DEBUG=false
DAYU_MYSQL_HOST=prod-mysql.example.com
DAYU_MYSQL_PASSWORD=secure_prod_password
DAYU_LOG_LEVEL=INFO
DAYU_LOG_FILE=/var/log/dayu-fault-rag/app.log
```

### 3. 代码中的配置管理

#### Settings类结构

```python
from dayu_fault_rag.config.settings import Settings, get_settings

# 获取配置实例（单例模式）
settings = get_settings()

# 访问配置
db_config = settings.database_config
milvus_config = settings.milvus_config
api_config = settings.api_config
```

#### 配置模块化

系统将配置按功能模块分组：

```python
class Settings(BaseSettings):
    # 通用配置
    environment: str = "development"
    project_name: str = "Dayu Fault RAG System"
    debug: bool = False
    
    @property
    def database_config(self) -> DatabaseConfig:
        """数据库配置模块"""
        return DatabaseConfig(...)
    
    @property
    def milvus_config(self) -> MilvusConfig:
        """Milvus配置模块"""
        return MilvusConfig(...)
    
    @property
    def embedding_config(self) -> EmbeddingConfig:
        """向量化配置模块"""
        return EmbeddingConfig(...)
```

## 配置模块详解

### 1. 数据库配置 (DatabaseConfig)

```python
class DatabaseConfig(BaseModel):
    host: str = "localhost"
    port: int = 3306
    username: str = "root"
    password: str = ""
    database: str = "itmp"
    charset: str = "utf8mb4"
    pool_size: int = 10
    max_overflow: int = 20
    
    # 业务配置
    table_name: str = "fault_info"
    report_type_filter: str = "故障"
    stage_filter: str = "已发布"
```

### 2. Milvus配置 (MilvusConfig)

```python
class MilvusConfig(BaseModel):
    host: str = "localhost"
    port: int = 19530
    username: str = ""
    password: str = ""
    collection_name: str = "fault_vectors"
    dimension: int = 2560  # Qwen3-Embedding-4B维度
    metric_type: str = "COSINE"
```

### 3. 向量化配置 (EmbeddingConfig)

```python
class EmbeddingConfig(BaseModel):
    base_url: str = "http://ray.ttyuyin.com:10001/ray-qwen3-emb"
    model_name: str = "Qwen/Qwen3-Embedding-4B"
    timeout: int = 30
    max_length: int = 512
    batch_size: int = 32
    dimension: int = 2560
```

### 4. ETL配置 (ETLConfig)

```python
class ETLConfig(BaseModel):
    batch_size: int = 100
    sync_interval: int = 3600  # 同步间隔（秒）
    chunk_size: int = 1000
    chunk_overlap: int = 200
    watermark_file: str = "etl_watermark.json"
    
    # 数据过滤配置
    enabled_filters: dict[str, str] = {
        "reportType": "故障", 
        "stage": "已发布"
    }
```

### 5. API配置 (APIConfig)

```python
class APIConfig(BaseModel):
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    cors_origins: list[str] = ["*"]
    docs_url: str = "/docs"
    openapi_url: str = "/openapi.json"
```

### 6. 日志配置 (LoggingConfig)

```python
class LoggingConfig(BaseModel):
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: str | None = None
    max_bytes: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
```

## 使用场景和最佳实践

### 1. 开发环境

```bash
# 设置环境
export DAYU_ENVIRONMENT=development

# 使用development.yaml配置
# 数据库使用本地MySQL
# 日志级别为DEBUG
# 启用API文档
```

### 2. 生产环境

```bash
# 设置环境
export DAYU_ENVIRONMENT=production

# 通过环境变量提供敏感信息
export DAYU_MYSQL_HOST=prod-mysql.example.com
export DAYU_MYSQL_PASSWORD=secure_password
export DAYU_MILVUS_HOST=prod-milvus.example.com
export DAYU_MILVUS_PASSWORD=secure_password

# 使用production.yaml配置
# 关闭调试模式
# 禁用API文档
# 启用监控和告警
```

### 3. Docker容器化部署

```dockerfile
# Dockerfile示例
FROM python:3.11-slim

# 复制配置文件
COPY config/ /app/config/

# 设置环境变量
ENV DAYU_ENVIRONMENT=production
ENV DAYU_LOG_LEVEL=INFO

# 通过docker-compose.yml提供敏感配置
```

```yaml
# docker-compose.yml示例
version: '3.8'
services:
  dayu-fault-rag:
    build: .
    environment:
      - DAYU_MYSQL_HOST=mysql
      - DAYU_MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - DAYU_MILVUS_HOST=milvus
    depends_on:
      - mysql
      - milvus
```

### 4. Kubernetes部署

```yaml
# ConfigMap for non-sensitive config
apiVersion: v1
kind: ConfigMap
metadata:
  name: dayu-config
data:
  DAYU_ENVIRONMENT: production
  DAYU_LOG_LEVEL: INFO

---
# Secret for sensitive config
apiVersion: v1
kind: Secret
metadata:
  name: dayu-secrets
type: Opaque
data:
  DAYU_MYSQL_PASSWORD: <base64-encoded-password>
  DAYU_MILVUS_PASSWORD: <base64-encoded-password>
```

## 配置验证和错误处理

### 1. 类型验证

```python
# Pydantic自动进行类型验证
try:
    settings = get_settings()
    print(f"API端口: {settings.api_config.port}")  # 自动转换为int
except ValidationError as e:
    print(f"配置验证失败: {e}")
```

### 2. 必需字段检查

```python
class DatabaseConfig(BaseModel):
    host: str = Field(..., description="数据库主机地址")  # 必需字段
    port: int = Field(default=3306, ge=1, le=65535)  # 端口范围验证
    password: str = Field(min_length=1, description="数据库密码")  # 最小长度验证
```

### 3. 自定义验证器

```python
from pydantic import validator

class MilvusConfig(BaseModel):
    dimension: int = Field(default=2560)
    
    @validator('dimension')
    def validate_dimension(cls, v):
        if v not in [768, 1024, 2560]:
            raise ValueError('不支持的向量维度')
        return v
```

## 配置热重载

### 1. 开发环境热重载

```python
# 开发环境支持配置热重载
import importlib
from dayu_fault_rag.config import settings

# 重新加载配置
importlib.reload(settings)
new_settings = settings.get_settings()
```

### 2. 生产环境配置更新

```bash
# 通过环境变量更新配置
export DAYU_LOG_LEVEL=DEBUG

# 重启服务应用新配置
kubectl rollout restart deployment/dayu-fault-rag
```

## 故障排除

### 1. 常见配置问题

**问题：配置文件找不到**
```bash
# 检查配置文件路径
ls -la config/
# 确保配置文件存在且可读
```

**问题：环境变量未生效**
```bash
# 检查环境变量是否设置
env | grep DAYU_
# 检查变量名是否正确（注意前缀和大小写）
```

**问题：数据库连接失败**
```python
# 检查数据库配置
settings = get_settings()
print(f"数据库连接URL: {settings.get_mysql_url()}")
```

### 2. 配置调试

```python
# 打印当前配置
from dayu_fault_rag.config.settings import get_settings

settings = get_settings()
print(f"当前环境: {settings.environment}")
print(f"调试模式: {settings.debug}")
print(f"数据库主机: {settings.database_config.host}")
```

### 3. 配置验证脚本

```python
#!/usr/bin/env python3
"""配置验证脚本"""

def validate_config():
    try:
        from dayu_fault_rag.config.settings import get_settings
        settings = get_settings()
        
        # 验证关键配置
        assert settings.database_config.host, "数据库主机未配置"
        assert settings.milvus_config.host, "Milvus主机未配置"
        assert settings.embedding_config.base_url, "向量化服务URL未配置"
        
        print("✅ 配置验证通过")
        return True
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

if __name__ == "__main__":
    validate_config()
```

## 总结

大禹故障RAG系统的配置管理采用现代化的分层配置架构：

1. **YAML配置文件**：提供环境特定的配置模板和默认值
2. **环境变量**：管理敏感信息和运行时配置覆盖
3. **Pydantic配置类**：提供类型安全和配置验证
4. **分环境管理**：开发、测试、生产环境独立配置

这种设计确保了配置的安全性、灵活性和可维护性，支持从开发到生产的全生命周期配置管理。 