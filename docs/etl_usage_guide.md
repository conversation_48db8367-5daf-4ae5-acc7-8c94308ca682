# ETL数据处理服务使用指南

## 概述

ETL数据处理服务是大禹平台故障相似性RAG系统的核心组件，负责从MySQL数据库抽取故障数据，转换为向量化文档，并加载到Milvus向量数据库中。

## 核心功能

### 1. 数据抽取（Extract）
- 从MySQL数据库抽取故障数据
- 支持全量和增量数据抽取
- 基于水印机制实现增量更新

### 2. 数据转换（Transform）
- 故障数据序列化为文本格式
- HTML和JSON字段的智能处理
- 文本分块处理，优化向量化效果
- 生成包含元数据的向量文档

### 3. 数据加载（Load）
- 批量向量化处理
- 向量数据加载到Milvus数据库
- 支持增量更新和错误恢复

## 使用方式

### 1. 命令行工具

#### 健康检查
```bash
python scripts/run_etl.py health
```

#### 全量ETL处理
```bash
python scripts/run_etl.py full
```

#### 增量ETL处理
```bash
python scripts/run_etl.py incremental
```

#### 查看ETL状态
```bash
python scripts/run_etl.py status
```

#### 自定义参数
```bash
python scripts/run_etl.py full --batch-size 50 --log-level DEBUG
```

### 2. 程序化调用

```python
from dayu_fault_rag.services.etl_service import create_etl_service

# 创建ETL服务
etl_service = await create_etl_service()

# 健康检查
if await etl_service.health_check():
    print("ETL服务正常")

# 运行全量ETL
stats = await etl_service.run_full_etl()
print(f"处理了 {stats.processed_count} 条记录")

# 运行增量ETL
stats = await etl_service.run_incremental_etl()
print(f"增量处理了 {stats.processed_count} 条记录")

# 获取ETL状态
status = await etl_service.get_etl_status()
print(f"最后更新时间: {status.last_update_time}")
```

## 配置说明

ETL服务的配置通过 `config/settings.py` 中的 `ETLConfig` 类进行管理：

```python
@dataclass
class ETLConfig:
    batch_size: int = 100          # 批处理大小
    text_chunk_size: int = 1000    # 文本分块大小
    text_chunk_overlap: int = 200  # 分块重叠大小
    watermark_file: str = "./data/etl_watermark.json"  # 水印文件路径
```

## 水印机制

ETL服务使用水印机制来跟踪处理进度：

- **last_update_time**: 最后处理的数据更新时间
- **last_processed_fid**: 最后处理的故障ID
- **total_processed**: 总处理记录数
- **last_etl_time**: 最后ETL执行时间

水印信息保存在JSON文件中，支持ETL任务的断点续传。

## 监控和日志

### 日志输出
ETL服务提供详细的日志输出，包括：
- 处理进度统计
- 错误信息和异常堆栈
- 性能指标（处理速度、耗时等）

### 状态监控
```python
# 获取详细的ETL统计信息
stats = await etl_service.get_etl_status()
print(f"""
ETL状态:
- 最后更新时间: {stats.last_update_time}
- 总处理记录数: {stats.total_processed}
- 最后ETL时间: {stats.last_etl_time}
""")
```

## 错误处理

ETL服务具备完善的错误处理机制：

1. **连接错误**: 自动重试数据库连接
2. **数据错误**: 记录问题数据，继续处理其他记录
3. **向量化错误**: 跳过有问题的文档，不影响整体流程
4. **存储错误**: 重试机制，避免数据丢失

## 性能优化

### 批处理优化
- 支持可配置的批处理大小
- 批量向量化处理，提高效率
- 批量数据库操作，减少网络开销

### 内存管理
- 流式处理大数据集
- 及时清理中间结果
- 可配置的文本分块策略

### 并发处理
- 异步处理模式
- 并发向量化操作
- 数据库连接池管理

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL和Milvus服务状态
   - 验证连接配置信息
   - 查看网络连接是否正常

2. **向量化服务异常**
   - 检查嵌入模型服务状态
   - 验证模型配置和API密钥
   - 查看服务日志获取详细错误信息

3. **水印文件损坏**
   - 删除水印文件重新开始全量ETL
   - 或手动修复水印文件中的JSON格式

4. **内存不足**
   - 减小批处理大小
   - 调整文本分块参数
   - 增加系统可用内存

### 调试建议

1. 使用DEBUG日志级别获取详细信息
2. 小批量测试验证配置正确性
3. 监控系统资源使用情况
4. 定期备份水印文件和重要数据

## 最佳实践

1. **定期运行增量ETL**: 建议设置定时任务每小时运行一次增量ETL
2. **监控处理状态**: 定期检查ETL状态和错误日志
3. **备份重要文件**: 定期备份水印文件和配置文件
4. **性能调优**: 根据系统资源调整批处理大小和并发参数
5. **错误告警**: 集成监控系统，及时发现和处理异常情况

## 扩展功能

ETL服务支持以下扩展：

1. **自定义数据源**: 可扩展支持其他数据库或文件格式
2. **自定义转换逻辑**: 支持插件化的数据转换处理器
3. **多目标加载**: 支持同时加载到多个向量数据库
4. **实时处理**: 可集成消息队列实现实时数据处理

这个ETL服务为大禹平台提供了稳定、高效、可扩展的数据处理能力，是整个故障相似性RAG系统的重要基础设施。 