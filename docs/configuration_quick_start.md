# 配置系统快速入门指南

## 🚀 快速开始

大禹故障RAG系统使用统一的 **pydantic_settings** 配置管理，所有配置都通过环境变量设置。

### 1. 复制配置模板

```bash
# 复制环境变量模板
cp env.example .env

# 编辑配置文件
vim .env  # 或使用你喜欢的编辑器
```

### 2. 基础配置

最少需要配置的环境变量：

```bash
# 数据库连接
DAYU_MYSQL_HOST=your-mysql-host
DAYU_MYSQL_PASSWORD=your-mysql-password
DAYU_MYSQL_DATABASE=itmp

# Milvus向量数据库
DAYU_MILVUS_HOST=your-milvus-host
DAYU_MILVUS_COLLECTION=fault_vectors
```

### 3. 验证配置

```bash
# 运行配置测试
python scripts/test_config.py
```

## 📋 环境变量清单

### 必需配置
- `DAYU_MYSQL_HOST` - MySQL数据库主机
- `DAYU_MYSQL_PASSWORD` - MySQL密码
- `DAYU_MILVUS_HOST` - Milvus向量数据库主机

### 可选配置
- `DAYU_ENVIRONMENT=development|production` - 运行环境
- `DAYU_DEBUG=true|false` - 调试模式
- `DAYU_LOG_LEVEL=DEBUG|INFO|WARNING|ERROR` - 日志级别
- `DAYU_API_PORT=8000` - API服务端口

## 🔧 不同环境配置

### 开发环境
```bash
DAYU_ENVIRONMENT=development
DAYU_DEBUG=true
DAYU_LOG_LEVEL=DEBUG
DAYU_MYSQL_HOST=localhost
DAYU_MILVUS_HOST=localhost
```

### 生产环境
```bash
DAYU_ENVIRONMENT=production
DAYU_DEBUG=false
DAYU_LOG_LEVEL=INFO
DAYU_MYSQL_HOST=prod-mysql.example.com
DAYU_MILVUS_HOST=prod-milvus.example.com
DAYU_LOG_FILE=/var/log/dayu-fault-rag/app.log
```

## 🐳 Docker部署

### docker-compose.yml
```yaml
version: '3.8'
services:
  dayu-fault-rag:
    build: .
    environment:
      - DAYU_ENVIRONMENT=production
      - DAYU_MYSQL_HOST=mysql
      - DAYU_MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - DAYU_MILVUS_HOST=milvus
    env_file:
      - .env
```

### Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dayu-fault-rag
spec:
  template:
    spec:
      containers:
      - name: app
        image: dayu-fault-rag:latest
        env:
        - name: DAYU_ENVIRONMENT
          value: production
        - name: DAYU_MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: password
```

## 🛠️ 编程使用

```python
from dayu_fault_rag.config.settings import get_settings

# 获取配置（单例模式）
settings = get_settings()

# 使用配置
print(f"当前环境: {settings.environment}")
print(f"MySQL URL: {settings.get_mysql_url()}")

# 获取模块化配置
db_config = settings.database_config
milvus_config = settings.milvus_config
api_config = settings.api_config
```

## ❓ 常见问题

### Q: 如何查看当前配置？
```bash
python scripts/test_config.py
```

### Q: 环境变量没有生效？
检查变量名是否有 `DAYU_` 前缀，并且大小写正确。

### Q: 如何重新加载配置？
```python
from dayu_fault_rag.config.settings import get_settings
get_settings.cache_clear()  # 清除缓存
settings = get_settings()   # 重新加载
```

### Q: 如何添加新的配置项？
1. 在 `Settings` 类中添加字段
2. 更新 `env.example` 文件
3. 在相应的配置模块中使用新字段

## 📚 相关文档

- [完整配置系统文档](configuration_system.md)
- [环境变量模板](../env.example)
- [配置测试脚本](../scripts/test_config.py) 