# 实现计划 - 大禹平台相似故障检索系统 (MVP版本)

## 任务概述

本实现计划基于MVP设计，专注于核心的相似故障检索功能，提供REST API和MCP工具接口两种访问方式。采用测试驱动开发方法，确保代码质量和功能正确性。

## 实现任务列表

- [x] 1. 项目基础设置和环境配置
  - 创建项目基础目录结构
  - 配置Python项目依赖管理（pyproject.toml）
  - 设置开发和生产环境配置文件
  - 配置日志系统和基础工具
  - _需求：10.1, 10.2_

- [x] 2. 核心数据模型实现
  - 实现故障信息模型（FaultInfo）
  - 实现向量文档模型（VectorDocument）
  - 添加数据验证和序列化支持
  - 编写模型单元测试
  - _需求：1.1, 1.2_

- [x] 3. 数据库客户端封装
  - [x] 3.1 MySQL客户端实现（已完全重构为ORM版本）
    - ✅ 完全重构为SQLAlchemy ORM方式，移除所有原生SQL执行
    - ✅ 使用SQLAlchemy 2.0的现代ORM模型定义(DeclarativeBase)
    - ✅ 实现异步Session管理和连接池
    - ✅ 可配置的数据过滤条件(reportType="故障", stage="已发布")
    - ✅ 类型安全的ORM查询和自动化字段映射
    - ✅ 完善的错误处理和SQLAlchemy异常管理
    - ✅ 针对只读RAG系统优化的查询接口
    - ✅ 完整的单元测试覆盖(21个测试全部通过)
    - _需求：1.1, 1.3_
  
  - ✅ 3.2 Milvus客户端实现
    - ✅ 实现Milvus连接管理
    - ✅ 实现向量存储和检索接口
    - ✅ 支持相似性搜索功能
    - ✅ 编写Milvus客户端单元测试
    - _需求：2.1, 2.2, 3.1_

- ✅ 4. 向量化服务实现
  - ✅ 集成Qwen3-Embedding-4B模型
  - ✅ 实现文本到向量的转换服务
  - ✅ 支持批量向量化处理
  - ✅ 添加向量化缓存机制
  - ✅ 编写向量化服务单元测试
  - _需求：2.1, 2.2_

- ✅ 5. ETL数据处理服务
  - ✅ 5.1 数据抽取功能
    - ✅ 实现从MySQL抽取故障数据
    - ✅ 支持全量和增量数据抽取
    - ✅ 实现基础的水印机制
    - _需求：1.3, 1.4, 9.1_
  
  - ✅ 5.2 数据转换功能
    - ✅ 实现故障数据的文本序列化
    - ✅ 处理JSON和HTML字段的转换
    - ✅ 实现简化的分块策略
    - ✅ 编写数据转换单元测试
    - _需求：1.5, 1.6, 1.7_
  
  - ✅ 5.3 数据加载功能
    - ✅ 实现向量数据的批量加载
    - ✅ 支持增量更新机制
    - ✅ 添加数据加载状态跟踪
    - ✅ 编写ETL服务集成测试
    - _需求：2.3, 9.1_

- [x] 6. 相似性检索服务实现（基于LangGraph重构完成）
  - ✅ **基于LangGraph的现代化工作流架构**
    - 使用LangGraph StateGraph构建6节点处理流程
    - 实现节点式处理：validate_input → get_target_fault → generate_vector → retrieve_similar → [apply_rerank] → format_results
    - 使用TypedDict进行类型安全的State管理
    - 支持条件路由和错误处理节点
  - ✅ **核心功能实现**
    - 基于故障ID的相似案例查找（`search_similar_faults`方法）
    - 支持结果排序、过滤和分页
    - 集成Qwen3 reranker进行结果重排序
    - 配置化参数：top_k, similarity_threshold, use_rerank等
  - ✅ **性能优化**
    - 智能预检索：rerank模式下检索更多结果用于重排序
    - 结果去重：自动排除自身和低于阈值的案例
    - 批量处理优化
  - ✅ **异常处理和监控**
    - 工作流级别的错误捕获和处理
    - 节点级错误隔离和降级机制
    - 完整的健康检查接口
  - ✅ **测试覆盖**
    - 15个单元测试用例（86%覆盖率）
    - 覆盖成功路径、异常路径、边界条件
    - 测试LangGraph工作流各节点功能
  - ✅ **向后兼容性**
    - 保留传统SimilarityService接口供过渡使用
    - 提供迁移指南和兼容性测试
  - _需求：3.1, 3.2, 3.3_

- [ ] 7. REST API接口实现
  - [ ] 7.1 FastAPI应用框架
    - 创建FastAPI应用实例
    - 配置CORS和中间件
    - 实现统一的错误处理
    - 添加请求日志记录
    - _需求：7.1, 7.2_
  
  - [ ] 7.2 API路由实现
    - 实现相似故障查询端点（GET /v1/faults/{fid}/similar）
    - 实现健康检查端点（GET /v1/health）
    - 添加请求参数验证
    - 实现响应数据格式化
    - 编写API端点单元测试
    - _需求：7.3, 7.4_

- [ ] 8. MCP工具接口实现
  - [ ] 8.1 MCP服务器框架
    - 实现MCP协议服务器
    - 配置工具注册和发现
    - 实现基础的错误处理
    - _需求：6.1, 6.2_
  
  - [ ] 8.2 MCP工具实现
    - 实现search_similar_faults工具
    - 实现query_fault_database工具
    - 添加工具参数验证
    - 编写MCP工具单元测试
    - _需求：6.3, 6.4_

- [ ] 9. 系统配置和服务集成
  - 实现统一的配置管理系统
  - 配置数据库连接参数
  - 集成所有服务组件
  - 实现应用启动和关闭逻辑
  - 编写系统集成测试
  - _需求：10.1, 10.2_

- [ ] 10. 错误处理和监控
  - 实现统一的异常处理机制
  - 添加关键操作的错误重试
  - 实现基础的健康检查
  - 添加性能监控点
  - 编写错误处理测试用例
  - _需求：7.5, 10.3_

- [ ] 11. 数据初始化脚本
  - 编写数据库初始化脚本
  - 实现ETL执行脚本
  - 创建示例数据和测试数据
  - 编写部署验证脚本
  - _需求：1.8, 9.2_

- [ ] 12. 集成测试和端到端测试
  - [ ] 12.1 集成测试
    - 编写API端点集成测试
    - 编写MCP工具集成测试
    - 测试数据库连接和操作
    - 测试ETL流程的完整性
    - _需求：全部需求验证_
  
  - [ ] 12.2 端到端测试
    - 编写完整的相似性检索流程测试
    - 测试REST API和MCP接口的一致性
    - 验证错误处理和恢复机制
    - 性能基准测试
    - _需求：3.4, 7.6_

- [ ] 13. 文档和部署准备
  - 编写API文档和接口说明
  - 创建部署指南和配置说明
  - 编写开发环境搭建文档
  - 创建用户使用示例
  - _需求：10.4_

## 架构演进说明

### 任务6的架构升级（重要更新）

在实现任务6的过程中，我们基于用户反馈进行了重要的架构升级，从传统的面向对象服务架构升级为基于**LangGraph**的现代化工作流架构：

#### 传统架构 vs LangGraph架构

| 方面 | 传统SimilarityService | LangGraph FaultSearchWorkflow |
|------|------------------------|-------------------------------|
| **架构模式** | 面向对象方法调用链 | 6节点工作流（StateGraph） |
| **状态管理** | 手动状态传递 | TypedDict类型安全状态管理 |
| **错误处理** | try-catch块嵌套 | 节点级错误隔离 + 错误处理节点 |
| **扩展性** | 需要修改类方法 | 添加/修改工作流节点 |
| **可观测性** | 分散日志 | 工作流可视化 + 节点追踪 |
| **测试** | 集成测试复杂 | 节点级单元测试 |
| **Rerank集成** | 额外方法调用 | 条件分支工作流节点 |

#### LangGraph工作流节点

1. **validate_input**: 输入参数验证
2. **get_target_fault**: 获取目标故障信息
3. **generate_vector**: 生成查询向量
4. **retrieve_similar**: 执行向量相似性搜索
5. **apply_rerank**: 条件性执行Qwen3 reranker重排序
6. **format_results**: 格式化最终结果
7. **handle_error**: 统一错误处理

#### 关键改进

- **现代化架构**: 使用LangGraph StateGraph替代传统服务类
- **类型安全**: 使用TypedDict确保工作流状态的类型安全
- **条件路由**: 基于状态的条件分支（是否使用rerank）
- **错误隔离**: 每个节点独立处理错误，避免级联失败
- **可测试性**: 支持节点级单元测试和集成测试
- **可扩展性**: 通过添加节点或修改边来扩展功能

#### 向后兼容性

- 保留`SimilarityService`供过渡使用
- 新的`FaultSearchWorkflow`提供相同的核心功能
- 提供迁移指南和兼容性测试

### 第一阶段：核心基础（任务1-4）
建立项目基础架构，实现核心数据模型和数据库客户端，为后续开发奠定基础。

### 第二阶段：数据处理（任务5-6）
实现ETL数据处理流程和相似性检索核心功能，这是系统的核心价值所在。

### 第三阶段：接口实现（任务7-8）
实现REST API和MCP工具接口，提供系统的对外服务能力。

### 第四阶段：系统完善（任务9-13）
完善系统配置、错误处理、测试覆盖和部署准备，确保系统的稳定性和可维护性。

## 技术约束和注意事项

1. **依赖管理**: 使用uv进行Python环境和依赖管理
2. **代码质量**: 所有代码完成后使用ruff进行格式化
3. **测试覆盖**: 每个模块必须包含对应的单元测试
4. **错误处理**: 所有外部调用必须包含适当的错误处理
5. **配置管理**: 敏感信息通过环境变量或配置文件管理
6. **日志记录**: 关键操作必须有适当的日志记录
7. **性能考虑**: 数据库操作和向量检索需要考虑性能优化

## 验收标准

每个任务完成后，需要满足以下验收标准：
- 代码通过所有单元测试
- 代码符合项目的编码规范
- 功能满足对应的需求描述
- 包含适当的错误处理和日志记录
- 通过代码审查（如适用） 
- 完成任务之后要更新当前 tasks.md