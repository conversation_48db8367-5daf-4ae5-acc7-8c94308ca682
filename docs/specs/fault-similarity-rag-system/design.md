# 设计文档 - 大禹平台相似故障检索系统 (MVP版本)

## 概述

大禹平台相似故障检索系统MVP版本专注于核心RAG功能，为运维人员提供基础的相似故障检索服务。系统采用简化架构，快速实现可用的故障相似性搜索功能。

### MVP设计目标

- **核心功能**：实现基础的相似故障检索功能
- **双重接口**：提供REST API和MCP工具接口
- **快速部署**：最小化依赖，快速上线验证
- **数据同步**：支持基础的数据抽取和向量化
- **扩展预留**：为后续功能扩展预留架构空间

### MVP设计原则

1. **功能聚焦**：专注相似性检索这一核心功能
2. **架构简化**：减少组件复杂性，提高开发效率
3. **快速迭代**：优先可用性，后续逐步优化
4. **技术栈收敛**：选择成熟稳定的技术组合

## MVP项目目录结构

```
dayu-agent/
├── src/
│   └── dayu/
│       ├── __init__.py
│       ├── models/                # 数据模型
│       │   ├── __init__.py
│       │   ├── fault.py           # 故障信息模型
│       │   └── vector_document.py # 向量文档模型
│       ├── database/              # 数据库操作
│       │   ├── __init__.py
│       │   ├── mysql_client.py    # MySQL客户端
│       │   └── milvus_client.py   # Milvus客户端
│       ├── services/              # 业务服务
│       │   ├── __init__.py
│       │   ├── embedding_service.py   # 向量化服务
│       │   ├── similarity_service.py  # 相似性检索服务
│       │   └── etl_service.py         # ETL数据处理服务
│       ├── api/                   # REST API
│       │   ├── __init__.py
│       │   ├── main.py           # FastAPI应用
│       │   └── routes.py         # API路由
│       ├── mcp/                   # MCP工具接口
│       │   ├── __init__.py
│       │   ├── server.py         # MCP服务器
│       │   └── tools.py          # 工具定义
│       └── config/               # 配置管理
│           ├── __init__.py
│           └── settings.py       # 配置类
├── tests/                        # 测试目录
│   ├── __init__.py
│   ├── test_models.py           # 模型测试
│   ├── test_services.py         # 服务测试
│   └── test_api.py              # API测试
├── scripts/                      # 运维脚本
│   ├── setup_database.py        # 数据库初始化
│   └── run_etl.py               # ETL执行脚本
├── config/                       # 配置文件
│   ├── development.yaml
│   └── production.yaml
├── pyproject.toml               # Python项目配置
├── requirements.txt             # 依赖管理
└── README.md                    # 项目说明
```

### MVP目录结构说明

#### 简化的模块组织
- **models/**: 核心数据模型，包含故障信息和向量文档模型
- **database/**: 数据库客户端封装，支持MySQL和Milvus
- **services/**: 业务逻辑层，实现核心功能服务
- **api/**: REST API接口，提供HTTP服务
- **mcp/**: MCP工具接口，提供Claude等AI工具集成
- **config/**: 配置管理，支持不同环境配置

#### 配置和运维
- **scripts/**: 基础运维脚本
- **config/**: 环境配置文件
- **tests/**: 基础测试覆盖

## MVP架构

### 简化架构图

```mermaid
graph TB
    subgraph "数据层"
        MySQL[(MySQL<br/>fault_info)]
        Milvus[(Milvus<br/>向量数据库)]
    end
    
    subgraph "ETL服务"
        ETL[ETL服务<br/>数据同步]
        Embedding[向量化服务<br/>Qwen3-Embedding]
    end
    
    subgraph "接口层"
        FastAPI[FastAPI<br/>REST接口]
        MCPServer[MCP服务器<br/>AI工具集成]
        SimilarityService[相似性检索服务]
    end
    
    MySQL --> ETL
    ETL --> Embedding
    Embedding --> Milvus
    
    FastAPI --> SimilarityService
    MCPServer --> SimilarityService
    SimilarityService --> MySQL
    SimilarityService --> Milvus
    
    Client[HTTP客户端] --> FastAPI
    Claude[Claude/AI工具] --> MCPServer
```

### MVP架构设计

#### 1. 数据层
- **MySQL数据库**：存储原始故障数据，支持基本查询
- **Milvus向量数据库**：存储向量化文档，支持相似性检索

#### 2. 服务层
- **ETL服务**：负责数据抽取、转换和向量化
- **相似性检索服务**：核心业务逻辑，实现相似故障查找
- **向量化服务**：集成Qwen3-Embedding模型

#### 3. 接口层
- **REST API**：提供HTTP接口，支持相似性查询
- **MCP工具接口**：提供Claude等AI工具的集成能力

## MVP组件和接口

### 核心组件设计

#### 1. 数据库客户端
- **MySQLClient**: MySQL数据库操作封装，支持故障数据查询
- **MilvusClient**: Milvus向量数据库操作，支持向量存储和相似性检索

#### 2. 业务服务
- **ETLService**: 数据ETL处理，从MySQL抽取数据并转换为向量
- **EmbeddingService**: 向量化服务，调用Qwen3-Embedding模型
- **SimilarityService**: 相似性检索核心逻辑，实现故障相似性查找

#### 3. 接口组件
- **FastAPI应用**: 提供REST HTTP接口
- **MCP服务器**: 实现MCP协议，集成AI工具

### API接口设计

#### REST API端点
- **GET /v1/faults/{fid}/similar**: 根据故障ID查找相似故障
- **GET /v1/health**: 系统健康检查

#### MCP工具接口
提供两个核心MCP工具：
- **search_similar_faults**: 根据故障ID查找相似的历史故障
- **query_fault_database**: 根据自然语言查询故障知识库

## MVP数据模型

### 核心数据模型

#### 1. 故障信息模型
- **FaultInfo**: 映射fault_info表的基础字段
  - 故障ID (fid)
  - 故障描述 (faultDescribe)
  - 原因描述 (causeDescribe)
  - 故障级别 (level)
  - 其他基础字段

#### 2. 向量文档模型
- **VectorDocument**: 向量化后的文档
  - 文档内容 (content)
  - 向量表示 (vector)
  - 故障ID (fid)
  - 元数据 (metadata)

### 数据转换策略

#### 简化分块策略
- **基础文本字段**: 直接使用 (faultDescribe、causeDescribe)
- **复合字段**: 简单序列化为文本
- **分块单位**: 按故障记录进行分块，保持完整性

## MVP错误处理

### 基础错误处理策略

#### 1. 数据库错误
- **连接错误**: 基础重试机制，最大重试3次
- **查询错误**: 记录错误日志，返回友好提示

#### 2. 服务错误
- **向量化失败**: 跳过失败记录，继续处理
- **检索超时**: 设置超时时间5秒，避免长时间等待

#### 3. API错误
- **输入验证**: 验证故障ID格式
- **系统异常**: 统一异常处理，返回标准错误格式

## MVP测试策略

### 基础测试覆盖

#### 1. 单元测试
- **测试工具**: pytest
- **测试覆盖**: 
  - 数据模型验证
  - 业务服务逻辑
  - 数据转换功能
- **Mock策略**: Mock外部依赖(数据库、embedding服务)

#### 2. 集成测试
- **测试范围**: API端点和MCP工具测试
- **工具**: pytest + FastAPI TestClient
- **测试场景**: 
  - 相似性查询API
  - 健康检查API
  - MCP工具调用
  - 错误处理流程

#### 3. 数据质量验证
- **ETL流程**: 验证数据抽取和转换的正确性
- **向量质量**: 基础的向量生成和检索测试

