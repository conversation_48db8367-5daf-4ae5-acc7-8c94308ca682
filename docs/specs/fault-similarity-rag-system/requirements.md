# 需求文档 - 大禹平台相似故障检索系统

## 引言

大禹平台相似故障检索系统是一个基于RAG（检索增强生成）技术的智能故障分析应用。系统旨在通过分析历史故障数据，为运维人员提供相似故障检索和智能问答功能，帮助快速定位问题根因，提升故障处理效率。

系统从MySQL数据库中的fault_info表获取故障数据，通过向量化索引技术建立知识库，并提供多种交互方式：直接问答、MCP工具接口和Web前端界面。

## 需求

### 需求 1 - 数据抽取与预处理

**用户故事：** 作为系统管理员，我希望系统能够自动从MySQL数据库中抽取故障数据并进行预处理，以便建立高质量的知识索引。

#### 验收标准

1. 当系统启动时，应能够连接到指定的MySQL数据库（生产环境：itmp_sys，测试环境：itmp）
2. 当执行数据抽取时，系统应能够从fault_info表中读取所有字段数据
3. 当检测到lastUpdateTime字段时，系统应实现基于水印的增量加载机制
4. 当首次运行时，系统应执行全量数据加载
5. 当后续运行时，系统应只处理lastUpdateTime大于上次水印值的记录
6. 当数据抽取失败时，系统应能够从上一个成功的检查点恢复
7. 当处理JSON字段（如playback、improveAction）时，系统应将其序列化为自然语言文本
8. 当处理HTML字段（如problem）时，系统应解析"五个为什么"逻辑树结构并转换为连贯段落

### 需求 2 - 向量化与知识索引

**用户故事：** 作为系统，我需要将故障文本数据转换为向量表示并存储到向量数据库中，以便支持语义相似性检索。

#### 验收标准

1. 当文本预处理完成时，系统应使用Qwen3-Embedding-4B模型生成向量嵌入
2. 当生成嵌入时，系统应能够区分查询文本和文档文本的嵌入模式
3. 当向量生成完成时，系统应将向量数据存储到Milvus向量数据库
4. 当存储向量时，系统应保留原始故障ID（fid）和关键元数据作为关联信息
5. 当文档过长时，系统应使用RecursiveCharacterTextSplitter进行智能分块
6. 当分块时，系统应保持语义完整性，避免破坏因果关系链条
7. 当向量存储失败时，系统应记录错误日志并支持重试机制

### 需求 3 - 相似故障检索功能

**用户故事：** 作为运维人员，我希望能够输入一个故障ID，系统返回与该故障相似的历史故障案例，以便参考处理经验。

#### 验收标准

1. 当用户提供故障ID时，系统应从MySQL数据库中获取完整的故障记录
2. 当获取目标故障后，系统应将其序列化为权威的文本表示
3. 当文本表示生成后，系统应使用该文本作为查询向量进行相似性搜索
4. 当执行搜索时，系统应返回Top-K个最相似的故障案例
5. 当返回结果时，每个相似故障应包含故障ID、描述摘要和相似度分数
6. 当目标故障ID不存在时，系统应返回友好的错误提示
7. 当相似度分数低于阈值时，系统应提示未找到足够相似的故障

### 需求 4 - 智能问答功能

**用户故事：** 作为运维人员，我希望能够用自然语言提问关于故障的问题，系统基于历史故障数据提供准确回答。

#### 验收标准

1. 当用户输入自然语言问题时，系统应理解问题的语义意图
2. 当问题理解完成后，系统应在向量数据库中检索相关的故障文档片段
3. 当检索到相关文档时，系统应将文档作为上下文发送给大语言模型
4. 当生成回答时，系统应仅基于检索到的上下文材料回答问题
5. 当上下文不足以回答问题时，系统应明确表示信息不足
6. 当回答生成完成时，系统应提供答案来源的故障ID引用
7. 当问题超出故障领域范围时，系统应礼貌拒绝并引导用户提问相关问题

### 需求 5 - LangGraph工作流编排

**用户故事：** 作为系统架构师，我需要系统使用LangGraph构建智能的查询路由和处理流程，以便根据不同用户意图提供个性化服务。

#### 验收标准

1. 当用户查询进入系统时，路由节点应分析查询意图并分类为相似性搜索或问答类型
2. 当识别为相似性搜索时，系统应路由到目标故障获取流程
3. 当识别为问答查询时，系统应直接路由到文档检索流程
4. 当查询意图不明确时，系统应路由到错误处理节点
5. 当流程执行过程中，系统应维护完整的状态信息用于追踪和调试
6. 当任何节点发生错误时，系统应能够优雅地处理并返回有意义的错误信息
7. 当工作流完成时，系统应记录完整的执行步骤日志

### 需求 6 - MCP工具接口

**用户故事：** 作为第三方应用开发者，我希望通过标准的MCP工具接口集成故障检索功能，以便在我的应用中使用该服务。

#### 验收标准

1. 当系统启动时，应提供符合MCP协议的工具接口
2. 当接收到工具调用请求时，系统应验证请求格式和参数有效性
3. 当执行故障检索时，系统应返回结构化的JSON响应
4. 当检索相似故障时，工具应接受故障ID参数并返回相似故障列表
5. 当执行问答查询时，工具应接受问题文本并返回答案和来源引用
6. 当发生错误时，工具应返回标准的错误响应格式
7. 当工具被调用时，系统应记录调用日志用于监控和调试

### 需求 7 - REST API服务

**用户故事：** 作为应用集成方，我希望通过REST API接口访问故障检索功能，以便实现服务间的松耦合集成。

#### 验收标准

1. 当API服务启动时，应在指定端口提供HTTP/HTTPS访问
2. 当接收到POST /v1/query请求时，系统应处理自然语言查询并返回答案
3. 当接收到GET /v1/faults/{fid}/similar请求时，系统应返回相似故障列表
4. 当API调用成功时，系统应返回200状态码和标准JSON响应格式
5. 当API调用失败时，系统应返回适当的HTTP错误码和错误信息
6. 当API文档生成时，系统应自动生成Swagger/OpenAPI规范文档
7. 当API访问时，系统应验证认证令牌或API密钥

### 需求 8 - Web前端界面

**用户故事：** 作为运维人员，我希望有一个简单直观的Web界面来验证和使用故障检索功能。

#### 验收标准

1. 当访问前端页面时，用户应看到清晰的应用标题和输入界面
2. 当用户输入查询内容时，界面应提供文本输入框和提交按钮
3. 当提交查询后，界面应显示加载指示器提示处理中
4. 当查询完成时，界面应以格式化方式展示答案内容
5. 当有来源引用时，界面应在可展开区域显示相关故障ID和摘要
6. 当发生错误时，界面应显示友好的错误提示信息
7. 当界面加载时，应具备响应式设计适配不同屏幕尺寸

### 需求 9 - 数据同步与监控

**用户故事：** 作为系统运维人员，我需要系统具备自动数据同步和完善的监控能力，以确保服务的可靠性和数据的时效性。

#### 验收标准

1. 当配置定时任务时，系统应每小时自动执行一次增量数据同步
2. 当数据同步执行时，系统应记录同步的数据量和处理时间
3. 当同步失败时，系统应发送告警通知相关人员
4. 当LangGraph执行时，系统应记录每个节点的执行状态和中间结果
5. 当API调用时，系统应记录请求响应时间、成功率等关键指标
6. 当向量数据库访问时，系统应监控查询延迟和可用性
7. 当系统资源使用超过阈值时，应触发相应的告警机制

### 需求 10 - 部署与运维

**用户故事：** 作为DevOps工程师，我需要系统支持容器化部署和CI/CD流程，以便实现自动化的构建、测试和部署。

#### 验收标准

1. 当系统打包时，应生成包含所有依赖的Docker镜像
2. 当部署到Kubernetes时，应提供完整的部署配置文件
3. 当代码提交时，CI/CD流水线应自动执行测试和构建
4. 当测试通过时，系统应自动部署到指定环境
5. 当部署完成时，应执行健康检查确认服务正常运行
6. 当配置更新时，系统应支持热更新或滚动更新
7. 当需要回滚时，系统应支持快速回滚到上一个稳定版本 