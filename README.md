# 大禹故障RAG系统 (Dayu Fault RAG System)

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![LangGraph](https://img.shields.io/badge/LangGraph-0.2.6+-green.svg)](https://github.com/langchain-ai/langgraph)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

基于RAG（检索增强生成）技术的智能故障分析应用，使用LangGraph构建现代化工作流，为运维人员提供相似故障检索和智能问答功能。

## ✨ 核心特性

- 🔍 **智能故障检索**: 基于向量相似性的故障案例检索
- 🤖 **LangGraph工作流**: 使用LangGraph构建的现代化RAG工作流
- 📊 **多数据源集成**: 支持MySQL和Milvus向量数据库
- 🔄 **自动ETL同步**: 增量数据同步和向量化处理
- 🛠️ **MCP工具接口**: 支持Claude等AI工具集成
- 📈 **重排序优化**: 集成Qwen3 reranker提升检索精度
- 🐳 **容器化部署**: 支持Docker和Docker Compose部署

## 🏗️ 系统架构

```mermaid
graph TB
    subgraph "数据层"
        MySQL[(MySQL<br/>fault_info)]
        Milvus[(Milvus<br/>向量数据库)]
    end
    
    subgraph "ETL服务"
        ETL[ETL服务<br/>数据同步]
        Embedding[向量化服务<br/>Qwen3-Embedding]
    end
    
    subgraph "LangGraph工作流"
        Workflow[故障搜索工作流<br/>6节点处理流程]
        Rerank[Qwen3 Reranker<br/>结果重排序]
    end
    
    subgraph "接口层"
        MCP[MCP服务器<br/>AI工具集成]
    end
    
    MySQL --> ETL
    ETL --> Embedding
    Embedding --> Milvus
    
    MCP --> Workflow
    Workflow --> Rerank
    Workflow --> MySQL
    Workflow --> Milvus
    
    Claude[Claude/AI工具] --> MCP
```

## 🚀 快速开始

### 环境要求

- Python 3.11+
- MySQL 5.7+
- Milvus 2.3+
- Docker & Docker Compose (可选)

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd dayu-agent

# 安装依赖 (推荐使用uv)
uv sync

# 或使用pip
pip install -e .
```

### 配置环境

```bash
# 复制环境变量配置文件
cp env.example .env

# 编辑配置文件
vim .env
```

关键配置项：

```env
# MySQL数据库配置
DAYU_MYSQL_HOST=localhost
DAYU_MYSQL_PORT=3306
DAYU_MYSQL_USERNAME=root
DAYU_MYSQL_PASSWORD=your_password
DAYU_MYSQL_DATABASE=itmp

# Milvus向量数据库配置
DAYU_MILVUS_HOST=localhost
DAYU_MILVUS_PORT=19530
DAYU_MILVUS_COLLECTION=fault_vectors

# 向量化服务配置
DAYU_EMBEDDING_BASE_URL=http://your-embedding-service:10001/ray-qwen3-emb
DAYU_EMBEDDING_MODEL=Qwen/Qwen3-Embedding-4B
```

### 初始化数据库

```bash
# 设置数据库表结构
python scripts/setup_database.py

# 执行全量ETL同步
python main.py etl full
```

### 启动服务

```bash
# 启动MCP服务 (默认)
python main.py

# 或明确指定服务类型
python main.py mcp

# 启动ETL后台服务
python main.py etl-service

# 执行增量ETL
python main.py etl incremental
```

## 🐳 Docker部署

### 使用Docker Compose

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f dayu-mcp
```

### 单独构建镜像

```bash
# 构建镜像
docker build -t dayu-fault-rag .

# 运行MCP服务
docker run -d --name dayu-mcp \
  -p 8001:8001 \
  --env-file .env \
  dayu-fault-rag python main.py mcp
```

## 📖 使用指南

### MCP工具集成

系统提供MCP (Model Context Protocol) 工具接口，可以与Claude等AI工具集成：

```python
# 在Claude中使用
# 工具名称: run_langgraph
# 参数: group_msgs (故障描述或群聊消息)

# 示例调用
await run_langgraph("用户反馈今天航海抽取的冰冰小灵通没有到账，麻烦核实下")
```

### ETL数据同步

```bash
# 全量同步 (首次运行)
python main.py etl full

# 增量同步 (日常运行)
python main.py etl incremental

# 后台服务模式 (自动定时同步)
python main.py etl-service
```

### LangGraph工作流

系统使用LangGraph构建6节点工作流：

1. **get_query_text**: 处理输入消息
2. **retrieve_similar**: 向量相似性检索
3. **apply_rerank**: Qwen3重排序优化
4. **chat**: LLM生成最终回答

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_workflow.py

# 生成覆盖率报告
pytest --cov=dayu_fault_rag --cov-report=html
```

## 📁 项目结构

```
dayu-agent/
├── src/dayu_fault_rag/          # 主要源码
│   ├── agent/                   # LangGraph工作流
│   ├── database/                # 数据库客户端
│   ├── models/                  # 数据模型
│   ├── services/                # 业务服务
│   ├── mcp/                     # MCP工具接口
│   ├── config/                  # 配置管理
│   └── utils/                   # 工具函数
├── tests/                       # 测试代码
├── scripts/                     # 运维脚本
├── docs/                        # 文档
├── main.py                      # 主入口
├── pyproject.toml              # 项目配置
└── docker-compose.yml          # Docker编排
```

## 🔧 配置说明

详细配置说明请参考：
- [配置系统文档](docs/configuration_system.md)
- [快速配置指南](docs/configuration_quick_start.md)
- [ETL使用指南](docs/etl_usage_guide.md)

## 📊 监控和日志

系统提供完整的监控和日志功能：

- **日志级别**: DEBUG, INFO, WARNING, ERROR
- **日志输出**: 控制台 + 文件 (可配置)
- **工作流追踪**: LangGraph节点级别追踪
- **性能监控**: 查询延迟、成功率等指标

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [LangGraph](https://github.com/langchain-ai/langgraph) - 现代化工作流框架
- [LangChain](https://github.com/langchain-ai/langchain) - LLM应用开发框架
- [Milvus](https://milvus.io/) - 向量数据库
- [FastMCP](https://github.com/jlowin/fastmcp) - MCP协议实现

---

**大禹团队** - 智能运维，从故障分析开始 🚀
