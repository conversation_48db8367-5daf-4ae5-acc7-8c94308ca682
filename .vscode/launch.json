{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "milvus_client",
            "type": "debugpy",
            "request": "launch",
            "program": "src/dayu_fault_rag/database/milvus_client.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "run_etl: health",
            "type": "debugpy",
            "request": "launch",
            "program": "scripts/run_etl.py",
            "args": [
                "health"
            ],
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "run_etl: full",
            "type": "debugpy",
            "request": "launch",
            "program": "scripts/run_etl.py",
            "args": [
                "full"
            ],
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "run_etl: incremental",
            "type": "debugpy",
            "request": "launch",
            "program": "scripts/run_etl.py",
            "args": [
                "incremental"
            ],
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "workflow",
            "type": "debugpy",
            "request": "launch",
            "program": "src/dayu_fault_rag/agent/workflow.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "main: mcp",
            "type": "debugpy",
            "request": "launch",
            "program": "main.py",
            "args": [
                "mcp"
            ],
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "main: etl-service",
            "type": "debugpy",
            "request": "launch",
            "program": "main.py",
            "args": [
                "etl-service"
            ],
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "main: incremental etl",
            "type": "debugpy",
            "request": "launch",
            "program": "main.py",
            "args": [
                "etl",
                "incremental"
            ],
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
        {
            "name": "main: incremental etl",
            "type": "debugpy",
            "request": "launch",
            "program": "main.py",
            "args": [
                "etl",
                "full"
            ],
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "justMyCode": false
        },
    ]
}